import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 样式导入
import 'element-plus/dist/index.css'
import './styles/index.scss'

// 第三方库
import VueLazyload from 'vue3-lazyload'
import AOS from 'aos'
import 'aos/dist/aos.css'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(VueLazyload, {
  loading: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
  error: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
  lifecycle: {
    loading: (el: HTMLElement) => {
      el.style.filter = 'blur(5px)'
    },
    loaded: (el: HTMLElement) => {
      el.style.filter = 'none'
    }
  }
})

// 初始化AOS动画
AOS.init({
  duration: 800,
  easing: 'ease-in-out',
  once: true,
  offset: 100
})

// 挂载应用
app.mount('#app')
