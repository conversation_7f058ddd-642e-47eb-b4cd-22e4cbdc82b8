// 品牌色彩变量
$primary-color: #E53E3E;
$primary-dark: #C53030;
$primary-light: #FEB2B2;

$orange-color: #FF8C00;
$yellow-color: #FFD700;

// 中性色变量
$gray-50: #F7FAFC;
$gray-100: #EDF2F7;
$gray-200: #E2E8F0;
$gray-300: #CBD5E0;
$gray-400: #A0AEC0;
$gray-500: #718096;
$gray-600: #4A5568;
$gray-700: #2D3748;
$gray-800: #1A202C;
$gray-900: #171923;

// 功能色变量
$success-color: #38A169;
$warning-color: #DD6B20;
$error-color: #E53E3E;
$info-color: #3182CE;

// 字体变量
$font-family-zh: 'PingFang SC', 'Source Han Sans', 'Microsoft YaHei', sans-serif;
$font-family-en: 'Montserrat', 'Open Sans', sans-serif;

// 字号变量
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px
$font-size-5xl: 3rem;      // 48px

// 间距变量
$spacing-xs: 0.5rem;   // 8px
$spacing-sm: 1rem;     // 16px
$spacing-md: 1.5rem;   // 24px
$spacing-lg: 2rem;     // 32px
$spacing-xl: 3rem;     // 48px
$spacing-2xl: 4rem;    // 64px

// 圆角变量
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// 阴影变量
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
$shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);

// 断点变量
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 容器变量
$container-max-width: 1200px;
$container-padding: 1.5rem;

// 过渡变量
$transition-fast: 0.15s ease;
$transition-base: 0.3s ease;
$transition-slow: 0.5s ease;

// Z-index 变量
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
