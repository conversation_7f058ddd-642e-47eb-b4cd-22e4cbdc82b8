// Tailwind CSS 导入
@tailwind base;
@tailwind components;
@tailwind utilities;

// 变量导入
@import './variables.scss';

// 全局基础样式
@layer base {
  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: 'PingFang SC', 'Source Han Sans', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background-color: #f7fafc;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5em;
  }

  p {
    margin-bottom: 1em;
  }
}

// 全局组件样式
@layer components {
  // 容器样式
  .container {
    @apply max-w-container mx-auto px-6;
  }

  // 按钮样式
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-300 cursor-pointer;
    
    &.btn-primary {
      @apply bg-primary-500 text-white hover:bg-primary-600 active:bg-primary-700;
    }
    
    &.btn-secondary {
      @apply bg-transparent text-primary-500 border-2 border-primary-500 hover:bg-primary-500 hover:text-white;
    }
    
    &.btn-outline {
      @apply bg-transparent text-gray-700 border-2 border-gray-300 hover:border-primary-500 hover:text-primary-500;
    }
    
    &.btn-large {
      @apply px-8 py-4 text-lg;
    }
    
    &.btn-small {
      @apply px-4 py-2 text-sm;
    }
  }

  // 卡片样式
  .card {
    @apply bg-white rounded-xl shadow-card p-6 transition-all duration-300;
    
    &:hover {
      @apply shadow-hover transform -translate-y-1;
    }
  }

  // 标题样式
  .section-title {
    @apply text-3xl md:text-4xl font-bold text-gray-800 text-center mb-4;
  }

  .section-subtitle {
    @apply text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto;
  }

  // 输入框样式
  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-300;
  }

  // 标签样式
  .tag {
    @apply inline-block px-3 py-1 text-sm font-medium rounded-full;
    
    &.tag-primary {
      @apply bg-primary-100 text-primary-700;
    }
    
    &.tag-orange {
      @apply bg-orange-100 text-orange-700;
    }
    
    &.tag-yellow {
      @apply bg-yellow-100 text-yellow-700;
    }
  }
}

// 工具类样式
@layer utilities {
  // 文本渐变
  .text-gradient {
    background: linear-gradient(135deg, #E53E3E 0%, #FF8C00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  // 背景渐变
  .bg-gradient-primary {
    background: linear-gradient(135deg, #E53E3E 0%, #FF8C00 100%);
  }

  // 动画类
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  // 响应式隐藏
  .mobile-only {
    @apply block md:hidden;
  }

  .desktop-only {
    @apply hidden md:block;
  }

  // 文本省略
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  // 页面内容样式，配合透明头部导航栏
  .page-content {
    @apply relative;

    // 对于非首页，添加顶部间距以避免被固定头部遮挡
    &:not(.home-page) {
      padding-top: 100px;
    }
  }
}

// Element Plus 样式覆盖
.el-button {
  &.el-button--primary {
    background-color: #E53E3E;
    border-color: #E53E3E;
    
    &:hover {
      background-color: #dc2626;
      border-color: #dc2626;
    }
  }
}

.el-input__wrapper {
  border-radius: 8px;
  
  &.is-focus {
    box-shadow: 0 0 0 1px #E53E3E inset;
  }
}

// 自定义滚动条
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}
