// 通用类型定义
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

export interface PaginationParams {
  page: number
  size: number
}

export interface PaginationResponse<T = any> {
  list: T[]
  pagination: {
    page: number
    size: number
    total: number
    pages: number
  }
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email?: string
  phone?: string
  avatar?: string
  role: 'admin' | 'editor' | 'franchisee' | 'user'
  status: number
  createdAt: string
  updatedAt: string
}

export interface UserProfile {
  id: number
  userId: number
  realName?: string
  gender?: 1 | 2
  birthday?: string
  city?: string
  address?: string
  company?: string
  position?: string
}

// 文章相关类型
export interface Article {
  id: number
  title: string
  slug?: string
  summary?: string
  content: string
  featuredImage?: string
  categoryId?: number
  authorId: number
  status: 'draft' | 'published' | 'archived'
  isFeatured: boolean
  views: number
  likes: number
  publishAt?: string
  createdAt: string
  updatedAt: string
  category?: Category
  author?: User
}

export interface Category {
  id: number
  name: string
  slug: string
  description?: string
  parentId: number
  sortOrder: number
  status: number
  createdAt: string
  updatedAt: string
}

// 加盟申请相关类型
export interface FranchiseApplication {
  id: number
  name: string
  phone: string
  email?: string
  city: string
  district?: string
  investmentBudget?: number
  experience?: string
  message?: string
  status: 'pending' | 'processing' | 'approved' | 'rejected'
  assignedTo?: number
  processedAt?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface FranchiseFormData {
  name: string
  phone: string
  email?: string
  city: string
  district?: string
  investmentBudget?: number
  experience?: string
  message?: string
}

// 门店相关类型
export interface Store {
  id: number
  name: string
  code?: string
  address: string
  city: string
  province: string
  latitude?: number
  longitude?: number
  phone?: string
  openingHours?: string
  area?: number
  seatCount?: number
  franchiseeId?: number
  managerName?: string
  managerPhone?: string
  status: 'active' | 'inactive' | 'coming_soon' | 'closed'
  openingDate?: string
  images?: string[]
  description?: string
  createdAt: string
  updatedAt: string
}

// 轮播图类型
export interface Banner {
  id: number
  title: string
  image: string
  link?: string
  description?: string
  sortOrder: number
  status: number
  createdAt: string
  updatedAt: string
}

// 系统配置类型
export interface SystemConfig {
  id: number
  key: string
  value: string
  description?: string
  type: 'string' | 'number' | 'boolean' | 'json'
  group: string
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

// 联系表单类型
export interface ContactForm {
  name: string
  phone: string
  email?: string
  subject: string
  message: string
  type: 'general' | 'franchise' | 'complaint' | 'suggestion'
}

// 统计数据类型
export interface Statistics {
  totalStores: number
  totalFranchisees: number
  totalCities: number
  yearlyGrowth: number
  customerSatisfaction: number
  averageRevenue: number
}

// 菜品类型
export interface Product {
  id: number
  name: string
  description: string
  image: string
  category: string
  price?: number
  isRecommended: boolean
  sortOrder: number
  status: number
}

// 荣誉资质类型
export interface Honor {
  id: number
  title: string
  image: string
  description?: string
  year: number
  type: 'award' | 'certificate' | 'media'
  sortOrder: number
}

// 常见问题类型
export interface FAQ {
  id: number
  question: string
  answer: string
  category: string
  sortOrder: number
  status: number
}

// 投资分析类型
export interface InvestmentAnalysis {
  totalInvestment: number
  franchiseFee: number
  equipmentCost: number
  decorationCost: number
  workingCapital: number
  monthlyRevenue: number
  monthlyProfit: number
  paybackPeriod: number
  roi: number
}
