import api from '@/api'
import type { 
  Banner, 
  SystemConfig, 
  Statistics, 
  ContactForm,
  ApiResponse 
} from '@/types'

// 获取轮播图列表
export const getBanners = () => {
  return api.get<ApiResponse<Banner[]>>('/banners')
}

// 获取系统配置
export const getSystemConfigs = (group?: string) => {
  return api.get<ApiResponse<SystemConfig[]>>('/system/configs', {
    params: { group }
  })
}

// 获取统计数据
export const getStatistics = () => {
  return api.get<ApiResponse<Statistics>>('/statistics')
}

// 提交联系表单
export const submitContactForm = (data: ContactForm) => {
  return api.post<ApiResponse>('/contact/submit', data)
}

// 文件上传
export const uploadFile = (file: File, type: 'image' | 'document' = 'image') => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  
  return api.post<ApiResponse<{ url: string }>>('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取省市区数据
export const getRegions = (parentId?: number) => {
  return api.get<ApiResponse<Array<{
    id: number
    name: string
    code: string
    parentId: number
    level: number
  }>>>('/regions', {
    params: { parentId }
  })
}
