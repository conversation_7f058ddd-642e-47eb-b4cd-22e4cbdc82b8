import api from '@/api'
import type { 
  FranchiseApplication, 
  FranchiseFormData,
  InvestmentAnalysis,
  FAQ,
  ApiResponse,
  PaginationParams,
  PaginationResponse
} from '@/types'

// 提交加盟申请
export const submitFranchiseApplication = (data: FranchiseFormData) => {
  return api.post<ApiResponse<FranchiseApplication>>('/franchise/applications', data)
}

// 获取加盟申请列表（管理员）
export const getFranchiseApplications = (params: PaginationParams & {
  status?: string
  city?: string
  keyword?: string
}) => {
  return api.get<ApiResponse<PaginationResponse<FranchiseApplication>>>('/franchise/applications', {
    params
  })
}

// 获取加盟申请详情
export const getFranchiseApplication = (id: number) => {
  return api.get<ApiResponse<FranchiseApplication>>(`/franchise/applications/${id}`)
}

// 更新加盟申请状态
export const updateFranchiseApplicationStatus = (id: number, data: {
  status: 'pending' | 'processing' | 'approved' | 'rejected'
  notes?: string
}) => {
  return api.put<ApiResponse>(`/franchise/applications/${id}/status`, data)
}

// 获取投资分析数据
export const getInvestmentAnalysis = (params?: {
  city?: string
  storeSize?: 'small' | 'medium' | 'large'
}) => {
  return api.get<ApiResponse<InvestmentAnalysis>>('/franchise/investment-analysis', {
    params
  })
}

// 获取加盟FAQ
export const getFranchiseFAQs = () => {
  return api.get<ApiResponse<FAQ[]>>('/franchise/faqs')
}

// 获取加盟政策
export const getFranchisePolicy = () => {
  return api.get<ApiResponse<{
    franchiseFee: number
    royaltyFee: number
    advertisingFee: number
    minimumInvestment: number
    storeRequirements: {
      minArea: number
      maxArea: number
      location: string[]
    }
    supportServices: string[]
    contractTerm: number
  }>>('/franchise/policy')
}

// 计算投资回报
export const calculateROI = (data: {
  totalInvestment: number
  city: string
  storeSize: 'small' | 'medium' | 'large'
}) => {
  return api.post<ApiResponse<{
    monthlyRevenue: number
    monthlyProfit: number
    paybackPeriod: number
    roi: number
    breakEvenPoint: number
  }>>('/franchise/calculate-roi', data)
}
