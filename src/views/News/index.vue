<template>
  <div class="news-page">
    <Header />
    
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title" data-aos="fade-up">新闻中心</h1>
          <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            了解品牌最新动态和行业资讯
          </p>
        </div>
      </div>
    </section>
    
    <!-- 搜索和筛选 -->
    <section class="news-filter">
      <div class="container">
        <div class="filter-wrapper" data-aos="fade-up">
          <div class="filter-tabs">
            <button
              v-for="category in categories"
              :key="category.value"
              class="filter-tab"
              :class="{ active: activeCategory === category.value }"
              @click="setActiveCategory(category.value)"
            >
              {{ category.label }}
            </button>
          </div>
          
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索新闻标题或内容"
              @keyup.enter="searchNews"
            >
              <template #suffix>
                <el-button type="primary" @click="searchNews">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 新闻列表 -->
    <section class="news-content">
      <div class="container">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 主要内容 -->
          <div class="lg:col-span-3">
            <!-- 置顶新闻 -->
            <div v-if="featuredNews.length > 0" class="featured-news">
              <h2 class="section-title">置顶新闻</h2>
              <div class="featured-grid">
                <article 
                  v-for="news in featuredNews" 
                  :key="news.id"
                  class="featured-item"
                  data-aos="fade-up"
                >
                  <router-link :to="`/news/${news.id}`" class="featured-link">
                    <div class="featured-image">
                      <LazyImage
                        :src="news.featuredImage"
                        :alt="news.title"
                        container-class="w-full h-full"
                      />
                      <div class="featured-badge">置顶</div>
                    </div>
                    <div class="featured-content">
                      <h3 class="featured-title">{{ news.title }}</h3>
                      <p class="featured-summary">{{ news.summary }}</p>
                      <div class="featured-meta">
                        <span class="news-date">{{ formatDate(news.publishAt) }}</span>
                        <span class="news-views">{{ news.views }} 阅读</span>
                      </div>
                    </div>
                  </router-link>
                </article>
              </div>
            </div>
            
            <!-- 普通新闻列表 -->
            <div class="news-list">
              <h2 class="section-title">最新资讯</h2>
              <div class="news-grid">
                <article 
                  v-for="(news, index) in paginatedNews" 
                  :key="news.id"
                  class="news-item"
                  data-aos="fade-up"
                  :data-aos-delay="index * 100"
                >
                  <router-link :to="`/news/${news.id}`" class="news-link">
                    <div class="news-image">
                      <LazyImage
                        :src="news.featuredImage"
                        :alt="news.title"
                        container-class="w-full h-full"
                      />
                      <div class="news-category">
                        <span class="category-tag">{{ getCategoryLabel(news.category) }}</span>
                      </div>
                    </div>
                    <div class="news-content">
                      <h3 class="news-title">{{ news.title }}</h3>
                      <p class="news-summary">{{ news.summary }}</p>
                      <div class="news-meta">
                        <span class="news-date">{{ formatDate(news.publishAt) }}</span>
                        <span class="news-views">{{ news.views }} 阅读</span>
                      </div>
                    </div>
                  </router-link>
                </article>
              </div>
              
              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="currentPage"
                  :page-size="pageSize"
                  :total="filteredNews.length"
                  layout="prev, pager, next, jumper"
                  @current-change="onPageChange"
                />
              </div>
            </div>
          </div>
          
          <!-- 侧边栏 -->
          <div class="lg:col-span-1">
            <aside class="news-sidebar" data-aos="fade-left">
              <!-- 热门文章 -->
              <div class="sidebar-section">
                <h3 class="sidebar-title">热门文章</h3>
                <div class="hot-news-list">
                  <div 
                    v-for="(news, index) in hotNews" 
                    :key="news.id"
                    class="hot-news-item"
                  >
                    <router-link :to="`/news/${news.id}`" class="hot-news-link">
                      <div class="hot-news-rank">{{ index + 1 }}</div>
                      <div class="hot-news-content">
                        <h4 class="hot-news-title">{{ news.title }}</h4>
                        <div class="hot-news-meta">
                          <span class="hot-news-views">{{ news.views }} 阅读</span>
                        </div>
                      </div>
                    </router-link>
                  </div>
                </div>
              </div>
              
              <!-- 分类统计 -->
              <div class="sidebar-section">
                <h3 class="sidebar-title">分类统计</h3>
                <div class="category-stats">
                  <div 
                    v-for="stat in categoryStats" 
                    :key="stat.category"
                    class="category-stat-item"
                  >
                    <span class="stat-label">{{ stat.label }}</span>
                    <span class="stat-count">{{ stat.count }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 联系我们 -->
              <div class="sidebar-section">
                <h3 class="sidebar-title">联系我们</h3>
                <div class="contact-info">
                  <div class="contact-item">
                    <el-icon class="contact-icon">
                      <Phone />
                    </el-icon>
                    <span>************</span>
                  </div>
                  <div class="contact-item">
                    <el-icon class="contact-icon">
                      <Message />
                    </el-icon>
                    <span><EMAIL></span>
                  </div>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </div>
    </section>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import { 
  Search, 
  Phone, 
  Message 
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import LazyImage from '@/components/ui/LazyImage.vue'
import type { Article } from '@/types'

const activeCategory = ref('all')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(9)

const categories = ref([
  { value: 'all', label: '全部' },
  { value: 'news', label: '品牌动态' },
  { value: 'industry', label: '行业资讯' },
  { value: 'media', label: '媒体报道' },
  { value: 'franchise_story', label: '加盟故事' }
])

const newsList = ref<Article[]>([
  {
    id: 1,
    title: '你好签签荣获"2024年度最受欢迎餐饮品牌"奖',
    summary: '在刚刚结束的中国餐饮行业年度盛典上，你好签签凭借优质的产品和服务荣获"2024年度最受欢迎餐饮品牌"奖项。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 1,
    authorId: 1,
    status: 'published',
    isFeatured: true,
    views: 1250,
    likes: 0,
    publishAt: '2024-01-15',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15',
    category: 'news'
  },
  {
    id: 2,
    title: '全国第500家门店盛大开业，品牌发展再创新高',
    summary: '随着西安雁塔区旗舰店的盛大开业，你好签签全国门店数量正式突破500家，品牌发展迈上新台阶。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 1,
    authorId: 1,
    status: 'published',
    isFeatured: true,
    views: 980,
    likes: 0,
    publishAt: '2024-01-10',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
    category: 'news'
  },
  {
    id: 3,
    title: '春节期间营业时间调整通知',
    summary: '为了让员工与家人团聚，同时保障顾客用餐需求，现将春节期间各门店营业时间调整安排公布如下。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 1,
    authorId: 1,
    status: 'published',
    isFeatured: false,
    views: 756,
    likes: 0,
    publishAt: '2024-01-08',
    createdAt: '2024-01-08',
    updatedAt: '2024-01-08',
    category: 'news'
  },
  {
    id: 4,
    title: '2024年餐饮行业发展趋势分析',
    summary: '随着消费升级和数字化转型，餐饮行业正迎来新的发展机遇。本文深入分析2024年餐饮行业的发展趋势。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 2,
    authorId: 1,
    status: 'published',
    isFeatured: false,
    views: 642,
    likes: 0,
    publishAt: '2024-01-05',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-05',
    category: 'industry'
  },
  {
    id: 5,
    title: '央视财经频道专访你好签签创始人',
    summary: '央视财经频道《创业英雄汇》栏目专访你好签签创始人，深度解读品牌成功背后的故事。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 3,
    authorId: 1,
    status: 'published',
    isFeatured: false,
    views: 1180,
    likes: 0,
    publishAt: '2024-01-03',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-03',
    category: 'media'
  },
  {
    id: 6,
    title: '从打工族到成功创业者：李女士的加盟故事',
    summary: '李女士从一名普通的打工族，通过加盟你好签签，成功实现了创业梦想，月收入翻了三倍。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 4,
    authorId: 1,
    status: 'published',
    isFeatured: false,
    views: 890,
    likes: 0,
    publishAt: '2024-01-01',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    category: 'franchise_story'
  }
])

const featuredNews = computed(() => {
  return newsList.value.filter(news => news.isFeatured)
})

const filteredNews = computed(() => {
  let result = newsList.value.filter(news => !news.isFeatured)
  
  if (activeCategory.value !== 'all') {
    result = result.filter(news => news.category === activeCategory.value)
  }
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(news => 
      news.title.toLowerCase().includes(keyword) ||
      news.summary.toLowerCase().includes(keyword)
    )
  }
  
  return result.sort((a, b) => new Date(b.publishAt).getTime() - new Date(a.publishAt).getTime())
})

const paginatedNews = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredNews.value.slice(start, end)
})

const hotNews = computed(() => {
  return [...newsList.value]
    .sort((a, b) => b.views - a.views)
    .slice(0, 5)
})

const categoryStats = computed(() => {
  const stats = categories.value.map(category => {
    const count = category.value === 'all' 
      ? newsList.value.length 
      : newsList.value.filter(news => news.category === category.value).length
    
    return {
      category: category.value,
      label: category.label,
      count
    }
  })
  
  return stats.filter(stat => stat.category !== 'all')
})

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const getCategoryLabel = (category: string) => {
  const categoryItem = categories.value.find(item => item.value === category)
  return categoryItem?.label || '未分类'
}

const setActiveCategory = (category: string) => {
  activeCategory.value = category
  currentPage.value = 1
}

const searchNews = () => {
  currentPage.value = 1
}

const onPageChange = (page: number) => {
  currentPage.value = page
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

onMounted(() => {
  // 页面加载时的初始化逻辑
})
</script>

<style scoped lang="scss">
.news-page {
  .page-header {
    @apply bg-gradient-to-r from-primary-500 to-orange-500 text-white;
    padding: 120px 0 80px 0; // 增加顶部padding避免被导航栏遮挡
    
    .header-content {
      @apply text-center;
      
      .page-title {
        @apply text-4xl md:text-5xl font-bold mb-4;
      }
      
      .page-subtitle {
        @apply text-lg md:text-xl opacity-90;
      }
    }
  }
  
  .news-filter {
    @apply py-8 bg-white shadow-sm;
    
    .filter-wrapper {
      @apply flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0;
      
      .filter-tabs {
        @apply flex flex-wrap gap-4;
        
        .filter-tab {
          @apply px-4 py-2 rounded-full border-2 border-gray-300 text-gray-600 transition-all duration-300;
          @apply hover:border-primary-500 hover:text-primary-500;
          
          &.active {
            @apply bg-primary-500 border-primary-500 text-white;
          }
        }
      }
      
      .search-box {
        @apply w-full lg:w-80;
      }
    }
  }
  
  .news-content {
    @apply py-20;
    
    .section-title {
      @apply text-2xl font-bold text-gray-800 mb-8;
    }
    
    .featured-news {
      @apply mb-16;
      
      .featured-grid {
        @apply grid grid-cols-1 md:grid-cols-2 gap-8;
        
        .featured-item {
          .featured-link {
            @apply block bg-white rounded-xl overflow-hidden shadow-card transition-all duration-300;
            @apply hover:shadow-hover hover:transform hover:-translate-y-1;
            
            .featured-image {
              @apply relative h-64;
              
              .featured-badge {
                @apply absolute top-4 left-4 bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium;
              }
            }
            
            .featured-content {
              @apply p-6;
              
              .featured-title {
                @apply text-xl font-semibold text-gray-800 mb-3 line-clamp-2;
              }
              
              .featured-summary {
                @apply text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3;
              }
              
              .featured-meta {
                @apply flex justify-between items-center text-xs text-gray-500;
              }
            }
          }
        }
      }
    }
    
    .news-list {
      .news-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12;
        
        .news-item {
          .news-link {
            @apply block bg-white rounded-xl overflow-hidden shadow-card transition-all duration-300;
            @apply hover:shadow-hover hover:transform hover:-translate-y-1;
            
            .news-image {
              @apply relative h-48;
              
              .news-category {
                @apply absolute top-4 left-4;
                
                .category-tag {
                  @apply bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium;
                }
              }
            }
            
            .news-content {
              @apply p-6;
              
              .news-title {
                @apply text-lg font-semibold text-gray-800 mb-3 line-clamp-2;
              }
              
              .news-summary {
                @apply text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3;
              }
              
              .news-meta {
                @apply flex justify-between items-center text-xs text-gray-500;
              }
            }
          }
        }
      }
      
      .pagination-wrapper {
        @apply flex justify-center;
      }
    }
    
    .news-sidebar {
      @apply space-y-8;
      
      .sidebar-section {
        @apply bg-white rounded-xl p-6 shadow-card;
        
        .sidebar-title {
          @apply text-lg font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200;
        }
        
        .hot-news-list {
          @apply space-y-4;
          
          .hot-news-item {
            .hot-news-link {
              @apply flex items-start space-x-3 p-3 rounded-lg transition-all duration-300;
              @apply hover:bg-gray-50;
              
              .hot-news-rank {
                @apply w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold;
              }
              
              .hot-news-content {
                @apply flex-1;
                
                .hot-news-title {
                  @apply text-sm font-medium text-gray-800 mb-1 line-clamp-2;
                }
                
                .hot-news-meta {
                  .hot-news-views {
                    @apply text-xs text-gray-500;
                  }
                }
              }
            }
          }
        }
        
        .category-stats {
          @apply space-y-3;
          
          .category-stat-item {
            @apply flex justify-between items-center py-2;
            
            .stat-label {
              @apply text-gray-600;
            }
            
            .stat-count {
              @apply bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-sm font-medium;
            }
          }
        }
        
        .contact-info {
          @apply space-y-3;
          
          .contact-item {
            @apply flex items-center space-x-2 text-gray-600;
            
            .contact-icon {
              @apply text-primary-500;
            }
          }
        }
      }
    }
  }
}

// 文本截断样式
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
