<template>
  <div class="news-detail-page">
    <Header />
    
    <div class="container py-20">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 主要内容 -->
        <div class="lg:col-span-3">
          <article class="news-article" data-aos="fade-up">
            <!-- 文章头部 -->
            <header class="article-header">
              <div class="article-category">
                <span class="category-tag">{{ getCategoryLabel(article?.category) }}</span>
              </div>
              <h1 class="article-title">{{ article?.title }}</h1>
              <div class="article-meta">
                <div class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(article?.publishAt) }}</span>
                </div>
                <div class="meta-item">
                  <el-icon><View /></el-icon>
                  <span>{{ article?.views }} 阅读</span>
                </div>
                <div class="meta-item">
                  <el-icon><User /></el-icon>
                  <span>你好签签</span>
                </div>
              </div>
            </header>
            
            <!-- 特色图片 -->
            <div v-if="article?.featuredImage" class="article-image">
              <LazyImage
                :src="article.featuredImage"
                :alt="article.title"
                container-class="w-full h-96 rounded-xl overflow-hidden"
              />
            </div>
            
            <!-- 文章摘要 -->
            <div v-if="article?.summary" class="article-summary">
              <p>{{ article.summary }}</p>
            </div>
            
            <!-- 文章内容 -->
            <div class="article-content">
              <div v-html="articleContent"></div>
            </div>
            
            <!-- 文章底部 -->
            <footer class="article-footer">
              <div class="article-actions">
                <button @click="likeArticle" class="action-btn" :class="{ liked: isLiked }">
                  <el-icon><Star /></el-icon>
                  <span>点赞 ({{ article?.likes || 0 }})</span>
                </button>
                <button @click="shareArticle" class="action-btn">
                  <el-icon><Share /></el-icon>
                  <span>分享</span>
                </button>
              </div>
              
              <div class="article-navigation">
                <router-link v-if="prevArticle" :to="`/news/${prevArticle.id}`" class="nav-link prev">
                  <span class="nav-label">上一篇</span>
                  <span class="nav-title">{{ prevArticle.title }}</span>
                </router-link>
                <router-link v-if="nextArticle" :to="`/news/${nextArticle.id}`" class="nav-link next">
                  <span class="nav-label">下一篇</span>
                  <span class="nav-title">{{ nextArticle.title }}</span>
                </router-link>
              </div>
            </footer>
          </article>
        </div>
        
        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
          <aside class="news-sidebar" data-aos="fade-left">
            <!-- 相关文章 -->
            <div class="sidebar-section">
              <h3 class="sidebar-title">相关文章</h3>
              <div class="related-articles">
                <div 
                  v-for="relatedArticle in relatedArticles" 
                  :key="relatedArticle.id"
                  class="related-item"
                >
                  <router-link :to="`/news/${relatedArticle.id}`" class="related-link">
                    <div class="related-image">
                      <LazyImage
                        :src="relatedArticle.featuredImage"
                        :alt="relatedArticle.title"
                        container-class="w-full h-full"
                      />
                    </div>
                    <div class="related-content">
                      <h4 class="related-title">{{ relatedArticle.title }}</h4>
                      <div class="related-meta">
                        <span class="related-date">{{ formatDate(relatedArticle.publishAt) }}</span>
                      </div>
                    </div>
                  </router-link>
                </div>
              </div>
            </div>
            
            <!-- 热门文章 -->
            <div class="sidebar-section">
              <h3 class="sidebar-title">热门文章</h3>
              <div class="hot-articles">
                <div 
                  v-for="(hotArticle, index) in hotArticles" 
                  :key="hotArticle.id"
                  class="hot-item"
                >
                  <router-link :to="`/news/${hotArticle.id}`" class="hot-link">
                    <div class="hot-rank">{{ index + 1 }}</div>
                    <div class="hot-content">
                      <h4 class="hot-title">{{ hotArticle.title }}</h4>
                      <div class="hot-meta">
                        <span class="hot-views">{{ hotArticle.views }} 阅读</span>
                      </div>
                    </div>
                  </router-link>
                </div>
              </div>
            </div>
            
            <!-- 返回列表 -->
            <div class="sidebar-section">
              <router-link to="/news" class="btn btn-outline w-full">
                <el-icon><ArrowLeft /></el-icon>
                <span class="ml-2">返回新闻列表</span>
              </router-link>
            </div>
          </aside>
        </div>
      </div>
    </div>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { 
  Calendar, 
  View, 
  User, 
  Star, 
  Share, 
  ArrowLeft 
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import LazyImage from '@/components/ui/LazyImage.vue'
import type { Article } from '@/types'

const route = useRoute()
const article = ref<Article | null>(null)
const isLiked = ref(false)

// 模拟文章数据
const articles = ref<Article[]>([
  {
    id: 1,
    title: '你好签签荣获"2024年度最受欢迎餐饮品牌"奖',
    summary: '在刚刚结束的中国餐饮行业年度盛典上，你好签签凭借优质的产品和服务荣获"2024年度最受欢迎餐饮品牌"奖项。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 1,
    authorId: 1,
    status: 'published',
    isFeatured: true,
    views: 1250,
    likes: 45,
    publishAt: '2024-01-15',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15',
    category: 'news'
  },
  {
    id: 2,
    title: '全国第500家门店盛大开业，品牌发展再创新高',
    summary: '随着西安雁塔区旗舰店的盛大开业，你好签签全国门店数量正式突破500家，品牌发展迈上新台阶。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 1,
    authorId: 1,
    status: 'published',
    isFeatured: true,
    views: 980,
    likes: 32,
    publishAt: '2024-01-10',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-10',
    category: 'news'
  },
  {
    id: 3,
    title: '春节期间营业时间调整通知',
    summary: '为了让员工与家人团聚，同时保障顾客用餐需求，现将春节期间各门店营业时间调整安排公布如下。',
    content: '',
    featuredImage: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    categoryId: 1,
    authorId: 1,
    status: 'published',
    isFeatured: false,
    views: 756,
    likes: 18,
    publishAt: '2024-01-08',
    createdAt: '2024-01-08',
    updatedAt: '2024-01-08',
    category: 'news'
  }
])

const categories = [
  { value: 'news', label: '品牌动态' },
  { value: 'industry', label: '行业资讯' },
  { value: 'media', label: '媒体报道' },
  { value: 'franchise_story', label: '加盟故事' }
]

const articleContent = computed(() => {
  // 模拟文章内容
  return `
    <p>这是一篇关于${article.value?.title}的详细报道。</p>
    <p>在当今竞争激烈的餐饮市场中，你好签签凭借其独特的品牌定位和优质的产品服务，赢得了广大消费者的认可和喜爱。</p>
    <h3>品牌发展历程</h3>
    <p>自2018年品牌创立以来，你好签签始终坚持"品质第一、服务至上"的经营理念，不断创新产品和服务，为消费者提供更好的用餐体验。</p>
    <h3>未来发展规划</h3>
    <p>展望未来，你好签签将继续深耕串串香市场，不断提升品牌影响力，为更多消费者带来正宗的川渝美食体验。</p>
    <p>同时，我们也将加大对加盟商的支持力度，帮助更多创业者实现梦想，共同推动品牌的发展壮大。</p>
  `
})

const relatedArticles = computed(() => {
  if (!article.value) return []
  
  return articles.value
    .filter(item => item.id !== article.value!.id && item.category === article.value!.category)
    .slice(0, 3)
})

const hotArticles = computed(() => {
  return [...articles.value]
    .sort((a, b) => b.views - a.views)
    .slice(0, 5)
})

const prevArticle = computed(() => {
  if (!article.value) return null
  
  const currentIndex = articles.value.findIndex(item => item.id === article.value!.id)
  return currentIndex > 0 ? articles.value[currentIndex - 1] : null
})

const nextArticle = computed(() => {
  if (!article.value) return null
  
  const currentIndex = articles.value.findIndex(item => item.id === article.value!.id)
  return currentIndex < articles.value.length - 1 ? articles.value[currentIndex + 1] : null
})

const formatDate = (date?: string) => {
  if (!date) return ''
  return dayjs(date).format('YYYY年MM月DD日')
}

const getCategoryLabel = (category?: string) => {
  if (!category) return '未分类'
  const categoryItem = categories.find(item => item.value === category)
  return categoryItem?.label || '未分类'
}

const likeArticle = () => {
  if (!article.value) return
  
  isLiked.value = !isLiked.value
  if (isLiked.value) {
    article.value.likes = (article.value.likes || 0) + 1
    ElMessage.success('点赞成功！')
  } else {
    article.value.likes = Math.max((article.value.likes || 0) - 1, 0)
  }
}

const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value?.title,
      text: article.value?.summary,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    ElMessage.success('链接已复制到剪贴板！')
  }
}

onMounted(() => {
  const articleId = parseInt(route.params.id as string)
  article.value = articles.value.find(item => item.id === articleId) || null
  
  if (!article.value) {
    // 文章不存在，跳转到404页面
    // router.push('/404')
  } else {
    // 增加浏览量
    article.value.views += 1
  }
})
</script>

<style scoped lang="scss">
.news-detail-page {
  .news-article {
    @apply bg-white rounded-xl p-8 shadow-card;
    
    .article-header {
      @apply mb-8;
      
      .article-category {
        @apply mb-4;
        
        .category-tag {
          @apply bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium;
        }
      }
      
      .article-title {
        @apply text-3xl md:text-4xl font-bold text-gray-800 mb-6 leading-tight;
      }
      
      .article-meta {
        @apply flex flex-wrap gap-6 text-gray-600;
        
        .meta-item {
          @apply flex items-center space-x-2;
          
          .el-icon {
            @apply text-primary-500;
          }
        }
      }
    }
    
    .article-image {
      @apply mb-8;
    }
    
    .article-summary {
      @apply mb-8 p-6 bg-gray-50 rounded-lg border-l-4 border-primary-500;
      
      p {
        @apply text-lg text-gray-700 leading-relaxed italic;
      }
    }
    
    .article-content {
      @apply mb-8 prose prose-lg max-w-none;
      
      :deep(h3) {
        @apply text-xl font-semibold text-gray-800 mt-8 mb-4;
      }
      
      :deep(p) {
        @apply text-gray-600 leading-relaxed mb-4;
      }
    }
    
    .article-footer {
      @apply pt-8 border-t border-gray-200;
      
      .article-actions {
        @apply flex space-x-4 mb-8;
        
        .action-btn {
          @apply flex items-center space-x-2 px-4 py-2 rounded-lg border border-gray-300 text-gray-600 transition-all duration-300;
          @apply hover:border-primary-500 hover:text-primary-500;
          
          &.liked {
            @apply bg-primary-500 border-primary-500 text-white;
          }
        }
      }
      
      .article-navigation {
        @apply grid grid-cols-1 md:grid-cols-2 gap-4;
        
        .nav-link {
          @apply block p-4 bg-gray-50 rounded-lg transition-all duration-300;
          @apply hover:bg-gray-100;
          
          &.prev {
            @apply text-left;
          }
          
          &.next {
            @apply text-right;
          }
          
          .nav-label {
            @apply block text-sm text-gray-500 mb-1;
          }
          
          .nav-title {
            @apply block text-gray-800 font-medium line-clamp-2;
          }
        }
      }
    }
  }
  
  .news-sidebar {
    @apply space-y-8;
    
    .sidebar-section {
      @apply bg-white rounded-xl p-6 shadow-card;
      
      .sidebar-title {
        @apply text-lg font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200;
      }
      
      .related-articles {
        @apply space-y-4;
        
        .related-item {
          .related-link {
            @apply flex space-x-3 p-3 rounded-lg transition-all duration-300;
            @apply hover:bg-gray-50;
            
            .related-image {
              @apply w-16 h-16 rounded-lg overflow-hidden flex-shrink-0;
            }
            
            .related-content {
              @apply flex-1;
              
              .related-title {
                @apply text-sm font-medium text-gray-800 mb-1 line-clamp-2;
              }
              
              .related-meta {
                .related-date {
                  @apply text-xs text-gray-500;
                }
              }
            }
          }
        }
      }
      
      .hot-articles {
        @apply space-y-3;
        
        .hot-item {
          .hot-link {
            @apply flex items-start space-x-3 p-3 rounded-lg transition-all duration-300;
            @apply hover:bg-gray-50;
            
            .hot-rank {
              @apply w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0;
            }
            
            .hot-content {
              @apply flex-1;
              
              .hot-title {
                @apply text-sm font-medium text-gray-800 mb-1 line-clamp-2;
              }
              
              .hot-meta {
                .hot-views {
                  @apply text-xs text-gray-500;
                }
              }
            }
          }
        }
      }
    }
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
