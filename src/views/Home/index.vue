<template>
  <div class="home-page">
    <!-- 页面头部 -->
    <Header />
    
    <!-- 轮播图区域 -->
    <HeroBanner />
    
    <!-- 品牌核心价值 -->
    <BrandValues />
    
    <!-- 数据展示 -->
    <Statistics />
    
    <!-- 产品特色 -->
    <ProductHighlights />
    
    <!-- 加盟优势 -->
    <FranchiseAdvantages />
    
    <!-- 成功案例 -->
    <SuccessStories />
    
    <!-- 新闻动态 -->
    <LatestNews />
    
    <!-- CTA区域 -->
    <CallToAction />
    
    <!-- 页面底部 -->
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useCommonStore } from '@/stores'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import HeroBanner from './components/HeroBanner.vue'
import BrandValues from './components/BrandValues.vue'
import Statistics from './components/Statistics.vue'
import ProductHighlights from './components/ProductHighlights.vue'
import FranchiseAdvantages from './components/FranchiseAdvantages.vue'
import SuccessStories from './components/SuccessStories.vue'
import LatestNews from './components/LatestNews.vue'
import CallToAction from './components/CallToAction.vue'

const commonStore = useCommonStore()

onMounted(async () => {
  // 初始化页面数据
  await commonStore.initializeCommonData()
})
</script>

<style scoped lang="scss">
.home-page {
  @apply min-h-screen;
}
</style>
