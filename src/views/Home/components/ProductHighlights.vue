<template>
  <section class="product-highlights">
    <div class="container">
      <!-- 标题 -->
      <SectionTitle
        subtitle="特色产品"
        title="精选招牌美食"
        description="传承川渝经典，每一串都是匠心之作"
      />
      
      <!-- 产品展示 -->
      <div class="products-showcase">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 产品图片 -->
          <div class="products-gallery" data-aos="fade-right">
            <div class="main-image">
              <LazyImage
                :src="selectedProduct.image"
                :alt="selectedProduct.name"
                container-class="rounded-xl overflow-hidden shadow-lg"
              />
            </div>
            <div class="thumbnail-list">
              <div 
                v-for="product in products" 
                :key="product.id"
                class="thumbnail-item"
                :class="{ active: selectedProduct.id === product.id }"
                @click="selectProduct(product)"
              >
                <LazyImage
                  :src="product.image"
                  :alt="product.name"
                  container-class="rounded-lg overflow-hidden"
                />
              </div>
            </div>
          </div>
          
          <!-- 产品信息 -->
          <div class="products-info" data-aos="fade-left">
            <div class="product-details">
              <h3 class="product-name">{{ selectedProduct.name }}</h3>
              <p class="product-description">{{ selectedProduct.description }}</p>
              
              <div class="product-features">
                <div 
                  v-for="feature in selectedProduct.features" 
                  :key="feature"
                  class="feature-item"
                >
                  <el-icon class="feature-icon">
                    <Check />
                  </el-icon>
                  <span class="feature-text">{{ feature }}</span>
                </div>
              </div>
              
              <div class="product-actions">
                <router-link to="/products" class="btn btn-primary">
                  查看更多产品
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 制作工艺 -->
      <div class="cooking-process" data-aos="fade-up" data-aos-delay="400">
        <h3 class="process-title">传统制作工艺</h3>
        <div class="process-steps">
          <div 
            v-for="(step, index) in cookingSteps" 
            :key="step.id"
            class="step-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h4 class="step-title">{{ step.title }}</h4>
              <p class="step-description">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Check } from '@element-plus/icons-vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'

const products = ref([
  {
    id: 1,
    name: '经典牛肉串',
    description: '精选优质牛肉，切片均匀，肉质鲜嫩，配以秘制调料，麻辣鲜香，回味无穷。',
    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    features: ['优质牛肉', '秘制调料', '现串现煮', '麻辣鲜香']
  },
  {
    id: 2,
    name: '特色毛肚',
    description: '新鲜毛肚，口感爽脆，搭配特制蘸料，层次丰富，是串串香的经典搭配。',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    features: ['新鲜毛肚', '爽脆口感', '特制蘸料', '经典搭配']
  },
  {
    id: 3,
    name: '招牌鸭肠',
    description: '精选鸭肠，处理干净，口感嫩滑，配以香辣汤底，美味难挡。',
    image: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    features: ['精选鸭肠', '嫩滑口感', '香辣汤底', '美味难挡']
  }
])

const selectedProduct = ref(products.value[0])

const cookingSteps = ref([
  {
    id: 1,
    title: '精选食材',
    description: '严格筛选优质食材，确保新鲜度和品质'
  },
  {
    id: 2,
    title: '专业串制',
    description: '经验丰富的师傅手工串制，保证每串品质'
  },
  {
    id: 3,
    title: '秘制汤底',
    description: '传承川渝传统工艺，熬制香浓汤底'
  },
  {
    id: 4,
    title: '精心烹煮',
    description: '控制火候和时间，确保最佳口感'
  }
])

const selectProduct = (product: typeof products.value[0]) => {
  selectedProduct.value = product
}
</script>

<style scoped lang="scss">
.product-highlights {
  @apply py-20;
  
  .products-showcase {
    @apply mb-16;
    
    .products-gallery {
      .main-image {
        @apply mb-6;
      }
      
      .thumbnail-list {
        @apply flex space-x-4;
        
        .thumbnail-item {
          @apply w-20 h-20 cursor-pointer opacity-60 transition-all duration-300;
          @apply hover:opacity-100;
          
          &.active {
            @apply opacity-100 ring-2 ring-primary-500 ring-offset-2;
          }
        }
      }
    }
    
    .products-info {
      .product-details {
        .product-name {
          @apply text-2xl md:text-3xl font-bold text-gray-800 mb-4;
        }
        
        .product-description {
          @apply text-gray-600 leading-relaxed mb-6;
        }
        
        .product-features {
          @apply space-y-3 mb-8;
          
          .feature-item {
            @apply flex items-center space-x-3;
            
            .feature-icon {
              @apply text-primary-500;
            }
            
            .feature-text {
              @apply text-gray-700;
            }
          }
        }
      }
    }
  }
  
  .cooking-process {
    @apply bg-gray-50 rounded-xl p-8;
    
    .process-title {
      @apply text-2xl font-bold text-gray-800 text-center mb-12;
    }
    
    .process-steps {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
      
      .step-item {
        @apply text-center;
        
        .step-number {
          @apply w-12 h-12 bg-primary-500 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4;
        }
        
        .step-content {
          .step-title {
            @apply text-lg font-semibold text-gray-800 mb-2;
          }
          
          .step-description {
            @apply text-gray-600 text-sm leading-relaxed;
          }
        }
      }
    }
  }
}
</style>
