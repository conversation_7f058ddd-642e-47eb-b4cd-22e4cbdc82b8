<template>
  <section class="franchise-advantages">
    <div class="container">
      <!-- 标题 -->
      <SectionTitle
        subtitle="加盟优势"
        title="选择我们的理由"
        description="全方位支持，助您轻松创业成功"
      />
      
      <!-- 优势展示 -->
      <div class="advantages-grid">
        <div 
          v-for="(advantage, index) in advantages" 
          :key="advantage.id"
          class="advantage-item"
          data-aos="fade-up"
          :data-aos-delay="index * 100"
        >
          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon class="text-3xl">
                <component :is="advantage.icon" />
              </el-icon>
            </div>
            <h3 class="advantage-title">{{ advantage.title }}</h3>
            <p class="advantage-description">{{ advantage.description }}</p>
            <ul class="advantage-features">
              <li v-for="feature in advantage.features" :key="feature">
                <el-icon class="feature-check">
                  <Check />
                </el-icon>
                {{ feature }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- CTA区域 -->
      <div class="franchise-cta" data-aos="fade-up" data-aos-delay="600">
        <div class="cta-content">
          <h3 class="cta-title">准备开始您的创业之旅？</h3>
          <p class="cta-description">立即申请加盟，我们的专业团队将为您提供全程指导</p>
          <div class="cta-actions">
            <router-link to="/franchise" class="btn btn-primary btn-large">
              立即申请加盟
            </router-link>
            <a href="tel:************" class="btn btn-outline btn-large ml-4">
              咨询热线：************
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  Shop, 
  Setting, 
  UserFilled, 
  TrendCharts,
  Check
} from '@element-plus/icons-vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'

const advantages = ref([
  {
    id: 1,
    title: '品牌支持',
    description: '成熟的品牌体系，完善的VI识别系统',
    icon: Shop,
    features: [
      '统一品牌形象',
      '专业VI设计',
      '品牌宣传支持',
      '市场推广策略'
    ]
  },
  {
    id: 2,
    title: '技术支持',
    description: '专业的技术培训，标准化操作流程',
    icon: Setting,
    features: [
      '核心技术培训',
      '标准化流程',
      '持续技术更新',
      '远程技术支持'
    ]
  },
  {
    id: 3,
    title: '运营支持',
    description: '全方位运营指导，从开店到盈利',
    icon: UserFilled,
    features: [
      '选址指导',
      '装修设计',
      '人员培训',
      '运营管理'
    ]
  },
  {
    id: 4,
    title: '营销支持',
    description: '专业营销策划，提升门店业绩',
    icon: TrendCharts,
    features: [
      '开业策划',
      '促销活动',
      '线上推广',
      '会员管理'
    ]
  }
])
</script>

<style scoped lang="scss">
.franchise-advantages {
  @apply py-20 bg-gray-50;
  
  .advantages-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16;
    
    .advantage-item {
      .advantage-card {
        @apply bg-white rounded-xl p-8 shadow-card transition-all duration-300;
        @apply hover:shadow-hover hover:transform hover:-translate-y-2;
        
        .advantage-icon {
          @apply w-16 h-16 bg-primary-100 text-primary-500 rounded-xl flex items-center justify-center mb-6;
        }
        
        .advantage-title {
          @apply text-xl font-semibold text-gray-800 mb-3;
        }
        
        .advantage-description {
          @apply text-gray-600 mb-6;
        }
        
        .advantage-features {
          @apply space-y-2;
          
          li {
            @apply flex items-center space-x-2 text-sm text-gray-600;
            
            .feature-check {
              @apply text-primary-500 text-xs;
            }
          }
        }
        
        &:hover {
          .advantage-icon {
            @apply bg-primary-500 text-white;
          }
        }
      }
    }
  }
  
  .franchise-cta {
    @apply bg-white rounded-xl p-12 shadow-card text-center;
    
    .cta-content {
      .cta-title {
        @apply text-2xl md:text-3xl font-bold text-gray-800 mb-4;
      }
      
      .cta-description {
        @apply text-gray-600 mb-8 max-w-2xl mx-auto;
      }
      
      .cta-actions {
        @apply flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4;
      }
    }
  }
}
</style>
