<template>
  <section class="hero-banner">
    <div class="banner-container">
      <!-- 轮播图 -->
      <div class="banner-slider">
        <swiper
          :modules="modules"
          :slides-per-view="1"
          :loop="true"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: false,
          }"
          :pagination="{ clickable: true }"
          :navigation="true"
          :effect="'fade'"
          class="hero-swiper"
        >
          <swiper-slide v-for="banner in banners" :key="banner.id">
            <div class="banner-slide">
              <LazyImage
                :src="banner.image"
                :alt="banner.title"
                container-class="banner-image"
                image-class="w-full h-full object-cover"
              />
              <div class="banner-overlay"></div>
              <div class="banner-content">
                <div class="container">
                  <div class="content-wrapper">
                    <h1 class="banner-title" data-aos="fade-up">
                      {{ banner.title }}
                    </h1>
                    <p class="banner-description" data-aos="fade-up" data-aos-delay="200">
                      {{ banner.description }}
                    </p>
                    <div class="banner-actions" data-aos="fade-up" data-aos-delay="400">
                      <router-link to="/franchise" class="btn btn-primary btn-large">
                        立即加盟
                      </router-link>
                      <router-link to="/about" class="btn btn-secondary btn-large ml-4">
                        了解更多
                      </router-link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      
      <!-- 快捷入口 -->
      <div class="quick-actions">
        <div class="container">
          <div class="actions-grid">
            <div 
              v-for="action in quickActions" 
              :key="action.id"
              class="action-item"
              data-aos="fade-up"
              :data-aos-delay="action.id * 100"
            >
              <router-link :to="action.link" class="action-link">
                <div class="action-icon">
                  <el-icon class="text-2xl">
                    <component :is="action.icon" />
                  </el-icon>
                </div>
                <div class="action-content">
                  <h3 class="action-title">{{ action.title }}</h3>
                  <p class="action-desc">{{ action.description }}</p>
                </div>
                <div class="action-arrow">
                  <el-icon>
                    <ArrowRight />
                  </el-icon>
                </div>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules'
import { 
  Shop, 
  Food, 
  Location, 
  Phone, 
  ArrowRight 
} from '@element-plus/icons-vue'
import { useCommonStore } from '@/stores'
import LazyImage from '@/components/ui/LazyImage.vue'

// Swiper样式
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/effect-fade'

const modules = [Navigation, Pagination, Autoplay, EffectFade]
const commonStore = useCommonStore()

const banners = ref([
  {
    id: 1,
    title: '你好签签 市井串串',
    description: '传承正宗川渝串串香文化，融合现代餐饮管理理念',
    image: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80'
  },
  {
    id: 2,
    title: '品质保证 口味正宗',
    description: '精选优质食材，传统工艺制作，每一串都是匠心之作',
    image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80'
  },
  {
    id: 3,
    title: '加盟创业 共赢未来',
    description: '全程扶持，从选址到运营，助您轻松创业成功',
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80'
  }
])

const quickActions = ref([
  {
    id: 1,
    title: '立即加盟',
    description: '了解加盟政策',
    icon: Shop,
    link: '/franchise'
  },
  {
    id: 2,
    title: '产品展示',
    description: '查看特色菜品',
    icon: Food,
    link: '/products'
  },
  {
    id: 3,
    title: '门店查询',
    description: '找到附近门店',
    icon: Location,
    link: '/stores'
  },
  {
    id: 4,
    title: '联系我们',
    description: '获取更多信息',
    icon: Phone,
    link: '/contact'
  }
])

onMounted(() => {
  // 如果有从store获取的轮播图数据，则使用store数据
  if (commonStore.banners.length > 0) {
    banners.value = commonStore.banners
  }
})
</script>

<style scoped lang="scss">
.hero-banner {
  @apply relative;
  
  .banner-container {
    @apply relative;
    
    .banner-slider {
      @apply h-screen max-h-[800px] min-h-[600px];
      
      .hero-swiper {
        @apply w-full h-full;
        
        .banner-slide {
          @apply relative w-full h-full;
          
          .banner-image {
            @apply absolute inset-0;
          }
          
          .banner-overlay {
            @apply absolute inset-0 bg-black bg-opacity-40;
          }
          
          .banner-content {
            @apply absolute inset-0 flex items-center;
            
            .content-wrapper {
              @apply text-white max-w-2xl;
              
              .banner-title {
                @apply text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight;
              }
              
              .banner-description {
                @apply text-lg md:text-xl mb-8 leading-relaxed opacity-90;
              }
              
              .banner-actions {
                @apply flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4;
              }
            }
          }
        }
      }
    }
    
    .quick-actions {
      @apply absolute bottom-0 left-0 right-0 transform translate-y-1/2 z-10;
      
      .actions-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
        
        .action-item {
          .action-link {
            @apply block bg-white rounded-xl shadow-card p-6 transition-all duration-300;
            @apply hover:shadow-hover hover:transform hover:-translate-y-1;
            
            .action-icon {
              @apply w-12 h-12 bg-primary-100 text-primary-500 rounded-lg flex items-center justify-center mb-4;
            }
            
            .action-content {
              .action-title {
                @apply text-lg font-semibold text-gray-800 mb-2;
              }
              
              .action-desc {
                @apply text-gray-600 text-sm;
              }
            }
            
            .action-arrow {
              @apply text-primary-500 mt-4 opacity-0 transform translate-x-2 transition-all duration-300;
            }
            
            &:hover {
              .action-arrow {
                @apply opacity-100 transform translate-x-0;
              }
            }
          }
        }
      }
    }
  }
}

// Swiper自定义样式
:deep(.swiper-pagination) {
  @apply bottom-8;
  
  .swiper-pagination-bullet {
    @apply w-3 h-3 bg-white bg-opacity-50;
    
    &.swiper-pagination-bullet-active {
      @apply bg-white;
    }
  }
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  @apply text-white;
  
  &::after {
    @apply text-2xl;
  }
}
</style>
