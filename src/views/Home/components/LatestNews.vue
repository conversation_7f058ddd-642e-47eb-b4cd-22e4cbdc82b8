<template>
  <section class="latest-news">
    <div class="container">
      <!-- 标题 -->
      <SectionTitle
        subtitle="新闻动态"
        title="最新资讯"
        description="了解品牌最新动态和行业资讯"
      />
      
      <!-- 新闻列表 -->
      <div class="news-grid">
        <article 
          v-for="(news, index) in newsList" 
          :key="news.id"
          class="news-item"
          data-aos="fade-up"
          :data-aos-delay="index * 100"
        >
          <router-link :to="`/news/${news.id}`" class="news-link">
            <div class="news-image">
              <LazyImage
                :src="news.featuredImage"
                :alt="news.title"
                container-class="w-full h-full"
              />
              <div class="news-category">
                <span class="category-tag">{{ news.category }}</span>
              </div>
            </div>
            <div class="news-content">
              <h3 class="news-title">{{ news.title }}</h3>
              <p class="news-summary">{{ news.summary }}</p>
              <div class="news-meta">
                <span class="news-date">{{ formatDate(news.publishAt) }}</span>
                <span class="news-views">{{ news.views }} 阅读</span>
              </div>
            </div>
          </router-link>
        </article>
      </div>
      
      <!-- 查看更多 -->
      <div class="news-more" data-aos="fade-up" data-aos-delay="400">
        <router-link to="/news" class="btn btn-outline">
          查看更多新闻
        </router-link>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'

const newsList = ref([
  {
    id: 1,
    title: '你好签签荣获"2024年度最受欢迎餐饮品牌"奖',
    summary: '在刚刚结束的中国餐饮行业年度盛典上，你好签签凭借优质的产品和服务荣获"2024年度最受欢迎餐饮品牌"奖项。',
    featuredImage: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    category: '品牌动态',
    publishAt: '2024-01-15',
    views: 1250
  },
  {
    id: 2,
    title: '全国第500家门店盛大开业，品牌发展再创新高',
    summary: '随着西安雁塔区旗舰店的盛大开业，你好签签全国门店数量正式突破500家，品牌发展迈上新台阶。',
    featuredImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    category: '门店动态',
    publishAt: '2024-01-10',
    views: 980
  },
  {
    id: 3,
    title: '春节期间营业时间调整通知',
    summary: '为了让员工与家人团聚，同时保障顾客用餐需求，现将春节期间各门店营业时间调整安排公布如下。',
    featuredImage: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80',
    category: '公告通知',
    publishAt: '2024-01-08',
    views: 756
  }
])

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}
</script>

<style scoped lang="scss">
.latest-news {
  @apply py-20 bg-gray-50;
  
  .news-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12;
    
    .news-item {
      .news-link {
        @apply block bg-white rounded-xl overflow-hidden shadow-card transition-all duration-300;
        @apply hover:shadow-hover hover:transform hover:-translate-y-1;
        
        .news-image {
          @apply relative h-48 overflow-hidden;
          
          .news-category {
            @apply absolute top-4 left-4;
            
            .category-tag {
              @apply bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium;
            }
          }
        }
        
        .news-content {
          @apply p-6;
          
          .news-title {
            @apply text-lg font-semibold text-gray-800 mb-3 line-clamp-2;
          }
          
          .news-summary {
            @apply text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3;
          }
          
          .news-meta {
            @apply flex justify-between items-center text-xs text-gray-500;
            
            .news-date {
              @apply font-medium;
            }
            
            .news-views {
              @apply flex items-center space-x-1;
            }
          }
        }
      }
    }
  }
  
  .news-more {
    @apply text-center;
  }
}

// 文本截断样式
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
