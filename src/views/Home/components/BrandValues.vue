<template>
  <section class="brand-values">
    <div class="container">
      <!-- 标题区域 -->
      <SectionTitle
        subtitle="品牌价值"
        title="为什么选择你好签签"
        description="传承正宗川渝串串香文化，融合现代餐饮管理理念，为消费者提供优质的美食体验"
      />
      
      <!-- 价值展示 -->
      <div class="values-grid">
        <div 
          v-for="(value, index) in brandValues" 
          :key="value.id"
          class="value-item"
          data-aos="fade-up"
          :data-aos-delay="index * 100"
        >
          <div class="value-card">
            <div class="value-icon">
              <el-icon class="text-4xl">
                <component :is="value.icon" />
              </el-icon>
            </div>
            <div class="value-content">
              <h3 class="value-title">{{ value.title }}</h3>
              <p class="value-description">{{ value.description }}</p>
            </div>
            <div class="value-number">
              <span class="number">{{ String(index + 1).padStart(2, '0') }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 品牌故事预览 -->
      <div class="brand-story-preview" data-aos="fade-up" data-aos-delay="600">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div class="story-content">
            <h3 class="story-title">传承与创新的完美结合</h3>
            <p class="story-text">
              你好签签 市井串串始创于川渝大地，承载着深厚的巴蜀文化底蕴。我们坚持选用优质食材，
              传承传统制作工艺，同时融入现代餐饮管理理念，为每一位顾客提供正宗、美味、健康的串串香体验。
            </p>
            <p class="story-text">
              从第一家门店到如今遍布全国的连锁品牌，我们始终坚持"品质第一、服务至上"的经营理念，
              致力于将正宗的川渝串串香文化传播到全国各地。
            </p>
            <div class="story-actions">
              <router-link to="/about" class="btn btn-primary">
                了解品牌故事
              </router-link>
            </div>
          </div>
          <div class="story-image">
            <LazyImage
              src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
              alt="品牌故事"
              container-class="rounded-xl overflow-hidden shadow-lg"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Trophy,
  Star,
  Lock,
  Promotion,
  UserFilled,
  Setting
} from '@element-plus/icons-vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'

const brandValues = ref([
  {
    id: 1,
    title: '品质保证',
    description: '精选优质食材，严格品控流程，确保每一串都是精品',
    icon: Trophy
  },
  {
    id: 2,
    title: '口味正宗',
    description: '传承川渝传统工艺，地道麻辣鲜香，回味无穷',
    icon: Star
  },
  {
    id: 3,
    title: '食品安全',
    description: '严格遵循食品安全标准，让顾客吃得放心安心',
    icon: Lock
  },
  {
    id: 4,
    title: '品牌实力',
    description: '多年行业经验，成熟运营模式，值得信赖的品牌',
    icon: Promotion
  },
  {
    id: 5,
    title: '服务贴心',
    description: '专业服务团队，贴心周到服务，提升用餐体验',
    icon: UserFilled
  },
  {
    id: 6,
    title: '持续创新',
    description: '不断研发新品，优化服务流程，与时俱进发展',
    icon: Setting
  }
])
</script>

<style scoped lang="scss">
.brand-values {
  @apply py-20 bg-gray-50;
  
  .values-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20;
    
    .value-item {
      .value-card {
        @apply relative bg-white rounded-xl p-8 shadow-card transition-all duration-300;
        @apply hover:shadow-hover hover:transform hover:-translate-y-2;
        
        .value-icon {
          @apply w-16 h-16 bg-primary-100 text-primary-500 rounded-xl flex items-center justify-center mb-6;
        }
        
        .value-content {
          .value-title {
            @apply text-xl font-semibold text-gray-800 mb-3;
          }
          
          .value-description {
            @apply text-gray-600 leading-relaxed;
          }
        }
        
        .value-number {
          @apply absolute top-6 right-6;
          
          .number {
            @apply text-6xl font-bold text-gray-100;
          }
        }
        
        &:hover {
          .value-icon {
            @apply bg-primary-500 text-white;
          }
        }
      }
    }
  }
  
  .brand-story-preview {
    .story-content {
      .story-title {
        @apply text-2xl md:text-3xl font-bold text-gray-800 mb-6;
      }
      
      .story-text {
        @apply text-gray-600 leading-relaxed mb-4;
      }
      
      .story-actions {
        @apply mt-8;
      }
    }
    
    .story-image {
      @apply relative;
      
      &::before {
        content: '';
        @apply absolute -top-4 -left-4 w-full h-full bg-primary-100 rounded-xl -z-10;
      }
    }
  }
}
</style>
