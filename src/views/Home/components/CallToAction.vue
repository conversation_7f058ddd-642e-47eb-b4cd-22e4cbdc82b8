<template>
  <section class="call-to-action">
    <div class="cta-bg">
      <LazyImage
        src="https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80"
        alt="加盟背景"
        container-class="absolute inset-0"
        image-class="w-full h-full object-cover"
      />
      <div class="cta-overlay"></div>
    </div>
    
    <div class="container relative z-10">
      <div class="cta-content" data-aos="fade-up">
        <h2 class="cta-title">准备开启您的创业之旅？</h2>
        <p class="cta-description">
          加入你好签签大家庭，与我们一起传承川渝串串香文化，共创美好未来
        </p>
        
        <div class="cta-features">
          <div class="feature-item" data-aos="fade-up" data-aos-delay="100">
            <el-icon class="feature-icon">
              <Trophy />
            </el-icon>
            <span class="feature-text">品牌实力强</span>
          </div>
          <div class="feature-item" data-aos="fade-up" data-aos-delay="200">
            <el-icon class="feature-icon">
              <UserFilled />
            </el-icon>
            <span class="feature-text">全程扶持</span>
          </div>
          <div class="feature-item" data-aos="fade-up" data-aos-delay="300">
            <el-icon class="feature-icon">
              <TrendCharts />
            </el-icon>
            <span class="feature-text">盈利快</span>
          </div>
          <div class="feature-item" data-aos="fade-up" data-aos-delay="400">
            <el-icon class="feature-icon">
              <Lock />
            </el-icon>
            <span class="feature-text">风险低</span>
          </div>
        </div>
        
        <div class="cta-actions" data-aos="fade-up" data-aos-delay="500">
          <router-link to="/franchise" class="btn btn-primary btn-large">
            立即申请加盟
          </router-link>
          <a href="tel:************" class="btn btn-secondary btn-large ml-4">
            <el-icon class="mr-2">
              <Phone />
            </el-icon>
            ************
          </a>
        </div>
        
        <div class="cta-stats" data-aos="fade-up" data-aos-delay="600">
          <div class="stat-item">
            <AnimatedCounter
              :value="500"
              suffix="+"
              number-class="text-2xl font-bold text-white"
              suffix-class="text-xl font-semibold text-white"
            />
            <span class="stat-label">门店数量</span>
          </div>
          <div class="stat-item">
            <AnimatedCounter
              :value="100"
              suffix="+"
              number-class="text-2xl font-bold text-white"
              suffix-class="text-xl font-semibold text-white"
            />
            <span class="stat-label">覆盖城市</span>
          </div>
          <div class="stat-item">
            <AnimatedCounter
              :value="98"
              suffix="%"
              number-class="text-2xl font-bold text-white"
              suffix-class="text-xl font-semibold text-white"
            />
            <span class="stat-label">满意度</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import {
  Trophy,
  UserFilled,
  TrendCharts,
  Lock,
  Phone
} from '@element-plus/icons-vue'
import LazyImage from '@/components/ui/LazyImage.vue'
import AnimatedCounter from '@/components/ui/AnimatedCounter.vue'
</script>

<style scoped lang="scss">
.call-to-action {
  @apply relative py-20 overflow-hidden;
  
  .cta-bg {
    @apply absolute inset-0;
  }
  
  .cta-overlay {
    @apply absolute inset-0 bg-primary-900 bg-opacity-85;
  }
  
  .cta-content {
    @apply text-center text-white;
    
    .cta-title {
      @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-6;
    }
    
    .cta-description {
      @apply text-lg md:text-xl mb-12 max-w-3xl mx-auto leading-relaxed opacity-90;
    }
    
    .cta-features {
      @apply flex flex-wrap justify-center gap-8 mb-12;
      
      .feature-item {
        @apply flex items-center space-x-2 text-white;
        
        .feature-icon {
          @apply text-xl;
        }
        
        .feature-text {
          @apply font-medium;
        }
      }
    }
    
    .cta-actions {
      @apply flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 mb-16;
    }
    
    .cta-stats {
      @apply flex justify-center space-x-12 pt-8 border-t border-white border-opacity-20;
      
      .stat-item {
        @apply text-center;
        
        .stat-label {
          @apply block text-sm text-white text-opacity-80 mt-2;
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .call-to-action {
    .cta-content {
      .cta-features {
        @apply grid grid-cols-2 gap-4;
      }
      
      .cta-stats {
        @apply space-x-8;
      }
    }
  }
}
</style>
