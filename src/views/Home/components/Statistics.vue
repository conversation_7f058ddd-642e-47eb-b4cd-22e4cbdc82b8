<template>
  <section class="statistics">
    <div class="statistics-bg">
      <LazyImage
        src="https://images.unsplash.com/photo-1559329007-40df8a9345d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80"
        alt="统计背景"
        container-class="absolute inset-0"
        image-class="w-full h-full object-cover"
      />
      <div class="statistics-overlay"></div>
    </div>
    
    <div class="container relative z-10">
      <!-- 标题 -->
      <SectionTitle
        subtitle="发展成果"
        title="数字见证品牌实力"
        description="多年来的稳健发展，用数据说话，用实力证明"
        wrapper-class="text-white"
        title-class="text-white"
        description-class="text-white text-opacity-90"
      />
      
      <!-- 统计数据 -->
      <div class="stats-grid">
        <div 
          v-for="(stat, index) in statistics" 
          :key="stat.id"
          class="stat-item"
          data-aos="fade-up"
          :data-aos-delay="index * 100"
        >
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon class="text-3xl">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                <AnimatedCounter
                  :value="stat.value"
                  :suffix="stat.suffix"
                  :duration="2000"
                  number-class="text-4xl md:text-5xl font-bold text-white"
                  suffix-class="text-2xl md:text-3xl font-semibold text-white"
                />
              </div>
              <p class="stat-label">{{ stat.label }}</p>
              <p class="stat-description">{{ stat.description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 成就展示 -->
      <div class="achievements" data-aos="fade-up" data-aos-delay="800">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div 
            v-for="achievement in achievements" 
            :key="achievement.id"
            class="achievement-item"
          >
            <div class="achievement-icon">
              <el-icon class="text-2xl">
                <component :is="achievement.icon" />
              </el-icon>
            </div>
            <p class="achievement-text">{{ achievement.text }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  Shop, 
  Location, 
  UserFilled, 
  TrendCharts,
  Medal,
  Star,
  Trophy,
  Flag
} from '@element-plus/icons-vue'
import { useCommonStore } from '@/stores'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'
import AnimatedCounter from '@/components/ui/AnimatedCounter.vue'

const commonStore = useCommonStore()

const statistics = ref([
  {
    id: 1,
    value: 500,
    suffix: '+',
    label: '门店数量',
    description: '遍布全国各大城市',
    icon: Shop
  },
  {
    id: 2,
    value: 100,
    suffix: '+',
    label: '覆盖城市',
    description: '持续扩张中',
    icon: Location
  },
  {
    id: 3,
    value: 10000,
    suffix: '+',
    label: '服务顾客',
    description: '每日服务人次',
    icon: UserFilled
  },
  {
    id: 4,
    value: 98,
    suffix: '%',
    label: '满意度',
    description: '顾客满意度',
    icon: TrendCharts
  }
])

const achievements = ref([
  {
    id: 1,
    text: '中国餐饮百强品牌',
    icon: Medal
  },
  {
    id: 2,
    text: '消费者信赖品牌',
    icon: Star
  },
  {
    id: 3,
    text: '行业创新奖',
    icon: Trophy
  },
  {
    id: 4,
    text: '食品安全示范企业',
    icon: Flag
  }
])

onMounted(async () => {
  // 如果store中有统计数据，则使用store数据
  if (commonStore.statistics) {
    const storeStats = commonStore.statistics
    statistics.value = [
      {
        id: 1,
        value: storeStats.totalStores,
        suffix: '+',
        label: '门店数量',
        description: '遍布全国各大城市',
        icon: Shop
      },
      {
        id: 2,
        value: storeStats.totalCities,
        suffix: '+',
        label: '覆盖城市',
        description: '持续扩张中',
        icon: Location
      },
      {
        id: 3,
        value: storeStats.totalFranchisees,
        suffix: '+',
        label: '加盟商',
        description: '成功创业伙伴',
        icon: UserFilled
      },
      {
        id: 4,
        value: storeStats.customerSatisfaction,
        suffix: '%',
        label: '满意度',
        description: '顾客满意度',
        icon: TrendCharts
      }
    ]
  }
})
</script>

<style scoped lang="scss">
.statistics {
  @apply relative py-20 overflow-hidden;
  
  .statistics-bg {
    @apply absolute inset-0;
  }
  
  .statistics-overlay {
    @apply absolute inset-0 bg-primary-900 bg-opacity-80;
  }
  
  .stats-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16;
    
    .stat-item {
      .stat-card {
        @apply text-center;
        
        .stat-icon {
          @apply w-16 h-16 bg-white bg-opacity-20 text-white rounded-full flex items-center justify-center mx-auto mb-6;
        }
        
        .stat-content {
          .stat-number {
            @apply mb-2;
          }
          
          .stat-label {
            @apply text-xl font-semibold text-white mb-2;
          }
          
          .stat-description {
            @apply text-white text-opacity-80;
          }
        }
      }
    }
  }
  
  .achievements {
    @apply border-t border-white border-opacity-20 pt-12;
    
    .achievement-item {
      @apply text-center;
      
      .achievement-icon {
        @apply w-12 h-12 bg-white bg-opacity-20 text-white rounded-full flex items-center justify-center mx-auto mb-4;
      }
      
      .achievement-text {
        @apply text-white text-sm font-medium;
      }
    }
  }
}
</style>
