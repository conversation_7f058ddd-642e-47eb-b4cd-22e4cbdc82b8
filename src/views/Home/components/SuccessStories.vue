<template>
  <section class="success-stories">
    <div class="container">
      <!-- 标题 -->
      <SectionTitle
        subtitle="成功案例"
        title="加盟商的成功故事"
        description="真实的创业经历，见证品牌的力量"
      />
      
      <!-- 故事展示 -->
      <div class="stories-slider">
        <swiper
          :modules="modules"
          :slides-per-view="1"
          :space-between="30"
          :loop="true"
          :autoplay="{
            delay: 4000,
            disableOnInteraction: false,
          }"
          :pagination="{ clickable: true }"
          :breakpoints="{
            768: {
              slidesPerView: 2,
            },
            1024: {
              slidesPerView: 3,
            },
          }"
          class="stories-swiper"
        >
          <swiper-slide v-for="story in stories" :key="story.id">
            <div class="story-card" data-aos="fade-up">
              <div class="story-avatar">
                <LazyImage
                  :src="story.avatar"
                  :alt="story.name"
                  container-class="w-full h-full rounded-full overflow-hidden"
                />
              </div>
              <div class="story-content">
                <h3 class="story-name">{{ story.name }}</h3>
                <p class="story-location">{{ story.location }}</p>
                <p class="story-text">"{{ story.content }}"</p>
                <div class="story-stats">
                  <div class="stat-item">
                    <span class="stat-value">{{ story.monthlyRevenue }}</span>
                    <span class="stat-label">月营业额</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-value">{{ story.paybackPeriod }}</span>
                    <span class="stat-label">回本周期</span>
                  </div>
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Autoplay } from 'swiper/modules'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'

import 'swiper/css'
import 'swiper/css/pagination'

const modules = [Pagination, Autoplay]

const stories = ref([
  {
    id: 1,
    name: '张先生',
    location: '成都市锦江区',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80',
    content: '选择你好签签是我做过最正确的决定，从选址到开业，总部给了我全方位的支持。现在店里生意红火，每天都有很多回头客。',
    monthlyRevenue: '25万+',
    paybackPeriod: '8个月'
  },
  {
    id: 2,
    name: '李女士',
    location: '重庆市渝中区',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80',
    content: '作为一个餐饮新手，我很担心经营问题。但你好签签的培训体系很完善，运营指导也很到位，让我很快就上手了。',
    monthlyRevenue: '30万+',
    paybackPeriod: '6个月'
  },
  {
    id: 3,
    name: '王先生',
    location: '西安市雁塔区',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80',
    content: '品牌的知名度很高，顾客认可度也很好。加上总部的营销支持，开业第一个月就实现了盈利。',
    monthlyRevenue: '20万+',
    paybackPeriod: '10个月'
  },
  {
    id: 4,
    name: '陈女士',
    location: '广州市天河区',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80',
    content: '你好签签的产品质量很稳定，顾客满意度很高。总部的技术支持也很及时，遇到问题都能快速解决。',
    monthlyRevenue: '35万+',
    paybackPeriod: '7个月'
  }
])
</script>

<style scoped lang="scss">
.success-stories {
  @apply py-20;
  
  .stories-slider {
    .stories-swiper {
      @apply pb-12;
      
      .story-card {
        @apply bg-white rounded-xl p-8 shadow-card transition-all duration-300;
        @apply hover:shadow-hover;
        
        .story-avatar {
          @apply w-16 h-16 mx-auto mb-6;
        }
        
        .story-content {
          @apply text-center;
          
          .story-name {
            @apply text-lg font-semibold text-gray-800 mb-1;
          }
          
          .story-location {
            @apply text-sm text-gray-500 mb-4;
          }
          
          .story-text {
            @apply text-gray-600 leading-relaxed mb-6 italic;
          }
          
          .story-stats {
            @apply flex justify-center space-x-8 pt-4 border-t border-gray-100;
            
            .stat-item {
              @apply text-center;
              
              .stat-value {
                @apply block text-lg font-bold text-primary-500;
              }
              
              .stat-label {
                @apply text-xs text-gray-500;
              }
            }
          }
        }
      }
    }
  }
}

:deep(.swiper-pagination) {
  @apply bottom-0;
  
  .swiper-pagination-bullet {
    @apply bg-gray-300;
    
    &.swiper-pagination-bullet-active {
      @apply bg-primary-500;
    }
  }
}
</style>
