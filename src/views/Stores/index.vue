<template>
  <div class="stores-page">
    <Header />
    
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title" data-aos="fade-up">门店展示</h1>
          <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            遍布全国的门店网络，为您提供便捷的服务
          </p>
        </div>
      </div>
    </section>
    
    <!-- 门店搜索 -->
    <section class="store-search">
      <div class="container">
        <div class="search-wrapper" data-aos="fade-up">
          <div class="search-form">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-select v-model="searchForm.province" placeholder="选择省份" class="w-full" @change="onProvinceChange">
                  <el-option 
                    v-for="province in provinces" 
                    :key="province.code" 
                    :label="province.name" 
                    :value="province.code" 
                  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-select v-model="searchForm.city" placeholder="选择城市" class="w-full" @change="onCityChange">
                  <el-option 
                    v-for="city in cities" 
                    :key="city.code" 
                    :label="city.name" 
                    :value="city.code" 
                  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-input 
                  v-model="searchForm.keyword" 
                  placeholder="搜索门店名称或地址"
                  @keyup.enter="searchStores"
                >
                  <template #suffix>
                    <el-button type="primary" @click="searchStores">
                      <el-icon><Search /></el-icon>
                    </el-button>
                  </template>
                </el-input>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 门店列表 -->
    <section class="stores-content">
      <div class="container">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 门店列表 -->
          <div class="lg:col-span-2">
            <div class="stores-header">
              <h2 class="stores-title">门店列表</h2>
              <div class="stores-count">
                共找到 <span class="count-number">{{ filteredStores.length }}</span> 家门店
              </div>
            </div>
            
            <div class="stores-list">
              <div 
                v-for="store in paginatedStores" 
                :key="store.id"
                class="store-item"
                data-aos="fade-up"
                @click="selectStore(store)"
              >
                <div class="store-card">
                  <div class="store-image">
                    <LazyImage
                      :src="store.images?.[0] || 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'"
                      :alt="store.name"
                      container-class="w-full h-full"
                    />
                    <div class="store-status" :class="store.status">
                      {{ getStatusText(store.status) }}
                    </div>
                  </div>
                  <div class="store-info">
                    <h3 class="store-name">{{ store.name }}</h3>
                    <p class="store-address">
                      <el-icon class="address-icon">
                        <Location />
                      </el-icon>
                      {{ store.address }}
                    </p>
                    <div class="store-details">
                      <div class="detail-item">
                        <el-icon><Phone /></el-icon>
                        <span>{{ store.phone || '暂无电话' }}</span>
                      </div>
                      <div class="detail-item">
                        <el-icon><Clock /></el-icon>
                        <span>{{ store.openingHours || '10:00-22:00' }}</span>
                      </div>
                    </div>
                    <div class="store-actions">
                      <button @click.stop="navigateToStore(store)" class="btn btn-outline btn-small">
                        导航
                      </button>
                      <button @click.stop="callStore(store)" class="btn btn-primary btn-small ml-2">
                        电话
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                :page-size="pageSize"
                :total="filteredStores.length"
                layout="prev, pager, next"
                @current-change="onPageChange"
              />
            </div>
          </div>
          
          <!-- 地图区域 -->
          <div class="lg:col-span-1">
            <div class="map-wrapper" data-aos="fade-left">
              <div class="map-header">
                <h3 class="map-title">门店地图</h3>
              </div>
              <div class="map-container">
                <div class="map-placeholder">
                  <el-icon class="text-6xl text-gray-400">
                    <Location />
                  </el-icon>
                  <p class="text-gray-500 mt-4">地图加载中...</p>
                  <p class="text-sm text-gray-400 mt-2">
                    点击门店查看位置
                  </p>
                </div>
              </div>
              
              <!-- 选中门店信息 -->
              <div v-if="selectedStore" class="selected-store">
                <h4 class="selected-title">{{ selectedStore.name }}</h4>
                <p class="selected-address">{{ selectedStore.address }}</p>
                <div class="selected-actions">
                  <button @click="navigateToStore(selectedStore)" class="btn btn-primary btn-small">
                    导航到此
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 特色门店 -->
    <section class="featured-stores">
      <div class="container">
        <SectionTitle
          subtitle="特色门店"
          title="旗舰店展示"
          description="精心打造的旗舰门店，展现品牌魅力"
        />
        
        <div class="featured-grid">
          <div 
            v-for="(store, index) in featuredStores" 
            :key="store.id"
            class="featured-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="featured-card">
              <div class="featured-image">
                <LazyImage
                  :src="store.images?.[0] || 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'"
                  :alt="store.name"
                  container-class="w-full h-full"
                />
              </div>
              <div class="featured-content">
                <h3 class="featured-name">{{ store.name }}</h3>
                <p class="featured-address">{{ store.address }}</p>
                <p class="featured-description">{{ store.description }}</p>
                <div class="featured-stats">
                  <div class="stat-item">
                    <span class="stat-value">{{ store.area }}㎡</span>
                    <span class="stat-label">营业面积</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-value">{{ store.seatCount }}</span>
                    <span class="stat-label">座位数</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  Search, 
  Location, 
  Phone, 
  Clock 
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'
import type { Store } from '@/types'

const currentPage = ref(1)
const pageSize = ref(6)
const selectedStore = ref<Store | null>(null)

const searchForm = reactive({
  province: '',
  city: '',
  keyword: ''
})

const provinces = ref([
  { code: 'SC', name: '四川省' },
  { code: 'CQ', name: '重庆市' },
  { code: 'BJ', name: '北京市' },
  { code: 'SH', name: '上海市' },
  { code: 'GD', name: '广东省' }
])

const cities = ref<Array<{ code: string; name: string }>>([])

const stores = ref<Store[]>([
  {
    id: 1,
    name: '你好签签春熙路旗舰店',
    code: 'CD001',
    address: '四川省成都市锦江区春熙路123号',
    city: '成都市',
    province: '四川省',
    phone: '028-8888-0001',
    openingHours: '10:00-23:00',
    area: 200,
    seatCount: 80,
    status: 'active',
    openingDate: '2020-01-15',
    images: ['https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'],
    description: '位于成都最繁华的春熙路商圈，是我们的旗舰门店',
    createdAt: '2020-01-15',
    updatedAt: '2024-01-01'
  },
  {
    id: 2,
    name: '你好签签解放碑店',
    code: 'CQ001',
    address: '重庆市渝中区解放碑步行街456号',
    city: '重庆市',
    province: '重庆市',
    phone: '023-8888-0002',
    openingHours: '10:00-22:30',
    area: 150,
    seatCount: 60,
    status: 'active',
    openingDate: '2020-06-20',
    images: ['https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'],
    description: '重庆解放碑核心商圈，地理位置优越',
    createdAt: '2020-06-20',
    updatedAt: '2024-01-01'
  },
  {
    id: 3,
    name: '你好签签三里屯店',
    code: 'BJ001',
    address: '北京市朝阳区三里屯太古里789号',
    city: '北京市',
    province: '北京市',
    phone: '010-8888-0003',
    openingHours: '11:00-22:00',
    area: 180,
    seatCount: 70,
    status: 'active',
    openingDate: '2021-03-10',
    images: ['https://images.unsplash.com/photo-1559329007-40df8a9345d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'],
    description: '北京时尚地标三里屯，年轻人聚集地',
    createdAt: '2021-03-10',
    updatedAt: '2024-01-01'
  },
  {
    id: 4,
    name: '你好签签南京路店',
    code: 'SH001',
    address: '上海市黄浦区南京东路321号',
    city: '上海市',
    province: '上海市',
    phone: '021-8888-0004',
    openingHours: '10:30-22:00',
    area: 160,
    seatCount: 65,
    status: 'coming_soon',
    openingDate: '2024-03-01',
    images: ['https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'],
    description: '上海南京路步行街，即将盛大开业',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15'
  }
])

const featuredStores = computed(() => {
  return stores.value.filter(store => store.area && store.area >= 180)
})

const filteredStores = computed(() => {
  let result = stores.value
  
  if (searchForm.province) {
    result = result.filter(store => store.province.includes(searchForm.province))
  }
  
  if (searchForm.city) {
    result = result.filter(store => store.city.includes(searchForm.city))
  }
  
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()
    result = result.filter(store => 
      store.name.toLowerCase().includes(keyword) ||
      store.address.toLowerCase().includes(keyword)
    )
  }
  
  return result
})

const paginatedStores = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredStores.value.slice(start, end)
})

const getStatusText = (status: string) => {
  const statusMap = {
    active: '营业中',
    inactive: '暂停营业',
    coming_soon: '即将开业',
    closed: '已关闭'
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}

const onProvinceChange = () => {
  searchForm.city = ''
  // 模拟获取城市数据
  const cityMap: Record<string, Array<{ code: string; name: string }>> = {
    SC: [
      { code: 'CD', name: '成都市' },
      { code: 'MY', name: '绵阳市' },
      { code: 'DY', name: '德阳市' }
    ],
    CQ: [
      { code: 'YZ', name: '渝中区' },
      { code: 'JB', name: '江北区' },
      { code: 'YB', name: '渝北区' }
    ],
    BJ: [
      { code: 'CY', name: '朝阳区' },
      { code: 'HD', name: '海淀区' },
      { code: 'XC', name: '西城区' }
    ],
    SH: [
      { code: 'HP', name: '黄浦区' },
      { code: 'XH', name: '徐汇区' },
      { code: 'CN', name: '长宁区' }
    ],
    GD: [
      { code: 'GZ', name: '广州市' },
      { code: 'SZ', name: '深圳市' },
      { code: 'DG', name: '东莞市' }
    ]
  }
  
  cities.value = cityMap[searchForm.province] || []
}

const onCityChange = () => {
  searchStores()
}

const searchStores = () => {
  currentPage.value = 1
  // 搜索逻辑已在computed中处理
}

const selectStore = (store: Store) => {
  selectedStore.value = store
  // 这里可以添加地图定位逻辑
}

const navigateToStore = (store: Store) => {
  // 打开地图导航
  const address = encodeURIComponent(store.address)
  window.open(`https://uri.amap.com/navigation?to=${address}`, '_blank')
}

const callStore = (store: Store) => {
  if (store.phone) {
    window.open(`tel:${store.phone}`)
  }
}

const onPageChange = (page: number) => {
  currentPage.value = page
}

onMounted(() => {
  // 初始化时选择第一个门店
  if (stores.value.length > 0) {
    selectedStore.value = stores.value[0]
  }
})
</script>

<style scoped lang="scss">
.stores-page {
  .page-header {
    @apply bg-gradient-to-r from-primary-500 to-orange-500 text-white;
    padding: 120px 0 80px 0; // 增加顶部padding避免被导航栏遮挡
    
    .header-content {
      @apply text-center;
      
      .page-title {
        @apply text-4xl md:text-5xl font-bold mb-4;
      }
      
      .page-subtitle {
        @apply text-lg md:text-xl opacity-90;
      }
    }
  }
  
  .store-search {
    @apply py-12 bg-white shadow-sm;
    
    .search-wrapper {
      @apply max-w-4xl mx-auto;
    }
  }
  
  .stores-content {
    @apply py-20;
    
    .stores-header {
      @apply flex justify-between items-center mb-8;
      
      .stores-title {
        @apply text-2xl font-bold text-gray-800;
      }
      
      .stores-count {
        @apply text-gray-600;
        
        .count-number {
          @apply text-primary-500 font-semibold;
        }
      }
    }
    
    .stores-list {
      @apply space-y-6 mb-8;
      
      .store-item {
        @apply cursor-pointer;
        
        .store-card {
          @apply bg-white rounded-xl shadow-card transition-all duration-300 overflow-hidden;
          @apply hover:shadow-hover hover:transform hover:-translate-y-1;
          
          .store-image {
            @apply relative h-48;
            
            .store-status {
              @apply absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-medium;
              
              &.active {
                @apply bg-green-100 text-green-700;
              }
              
              &.coming_soon {
                @apply bg-yellow-100 text-yellow-700;
              }
              
              &.inactive {
                @apply bg-gray-100 text-gray-700;
              }
            }
          }
          
          .store-info {
            @apply p-6;
            
            .store-name {
              @apply text-xl font-semibold text-gray-800 mb-2;
            }
            
            .store-address {
              @apply flex items-center text-gray-600 mb-4;
              
              .address-icon {
                @apply mr-2 text-primary-500;
              }
            }
            
            .store-details {
              @apply space-y-2 mb-4;
              
              .detail-item {
                @apply flex items-center text-sm text-gray-600;
                
                .el-icon {
                  @apply mr-2 text-primary-500;
                }
              }
            }
            
            .store-actions {
              @apply flex space-x-2;
            }
          }
        }
      }
    }
    
    .pagination-wrapper {
      @apply flex justify-center;
    }
    
    .map-wrapper {
      @apply sticky top-8;
      
      .map-header {
        @apply mb-4;
        
        .map-title {
          @apply text-xl font-semibold text-gray-800;
        }
      }
      
      .map-container {
        @apply h-96 bg-gray-100 rounded-xl mb-4 overflow-hidden;
        
        .map-placeholder {
          @apply h-full flex flex-col items-center justify-center text-center;
        }
      }
      
      .selected-store {
        @apply bg-white rounded-xl p-4 shadow-card;
        
        .selected-title {
          @apply font-semibold text-gray-800 mb-1;
        }
        
        .selected-address {
          @apply text-sm text-gray-600 mb-3;
        }
        
        .selected-actions {
          @apply text-center;
        }
      }
    }
  }
  
  .featured-stores {
    @apply py-20 bg-gray-50;
    
    .featured-grid {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
      
      .featured-item {
        .featured-card {
          @apply bg-white rounded-xl overflow-hidden shadow-card transition-all duration-300;
          @apply hover:shadow-hover hover:transform hover:-translate-y-1;
          
          .featured-image {
            @apply h-48;
          }
          
          .featured-content {
            @apply p-6;
            
            .featured-name {
              @apply text-lg font-semibold text-gray-800 mb-2;
            }
            
            .featured-address {
              @apply text-gray-600 text-sm mb-3;
            }
            
            .featured-description {
              @apply text-gray-600 text-sm mb-4;
            }
            
            .featured-stats {
              @apply flex justify-between pt-4 border-t border-gray-100;
              
              .stat-item {
                @apply text-center;
                
                .stat-value {
                  @apply block text-lg font-bold text-primary-500;
                }
                
                .stat-label {
                  @apply text-xs text-gray-500;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
