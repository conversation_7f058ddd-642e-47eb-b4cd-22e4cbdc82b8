<template>
  <div class="products-page">
    <Header />
    
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title" data-aos="fade-up">产品展示</h1>
          <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            精选优质食材，传承川渝经典，每一串都是匠心之作
          </p>
        </div>
      </div>
    </section>
    
    <!-- 产品分类 -->
    <section class="product-categories">
      <div class="container">
        <div class="category-tabs">
          <button
            v-for="category in categories"
            :key="category.id"
            class="category-tab"
            :class="{ active: activeCategory === category.id }"
            @click="setActiveCategory(category.id)"
          >
            {{ category.name }}
          </button>
        </div>
        
        <div class="products-grid">
          <div 
            v-for="(product, index) in filteredProducts" 
            :key="product.id"
            class="product-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="product-card">
              <div class="product-image">
                <LazyImage
                  :src="product.image"
                  :alt="product.name"
                  container-class="w-full h-full"
                />
                <div v-if="product.isRecommended" class="product-badge">
                  <span>推荐</span>
                </div>
              </div>
              <div class="product-content">
                <h3 class="product-name">{{ product.name }}</h3>
                <p class="product-description">{{ product.description }}</p>
                <div v-if="product.price" class="product-price">
                  ¥{{ product.price }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 制作工艺 -->
    <section class="cooking-process">
      <div class="container">
        <SectionTitle
          subtitle="制作工艺"
          title="传统川渝工艺"
          description="严格遵循传统制作工艺，确保每一串的品质"
        />
        
        <div class="process-steps">
          <div 
            v-for="(step, index) in processSteps" 
            :key="step.id"
            class="step-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="step-image">
              <LazyImage
                :src="step.image"
                :alt="step.title"
                container-class="w-full h-full rounded-lg overflow-hidden"
              />
            </div>
            <div class="step-content">
              <div class="step-number">{{ index + 1 }}</div>
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 门店环境 -->
    <section class="store-environment">
      <div class="container">
        <SectionTitle
          subtitle="门店环境"
          title="舒适的用餐环境"
          description="现代化的装修风格，营造温馨舒适的用餐氛围"
        />
        
        <div class="environment-gallery">
          <div 
            v-for="(env, index) in environments" 
            :key="env.id"
            class="env-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <LazyImage
              :src="env.image"
              :alt="env.title"
              container-class="w-full h-64 rounded-xl overflow-hidden shadow-lg"
            />
            <h3 class="env-title">{{ env.title }}</h3>
          </div>
        </div>
      </div>
    </section>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'

const activeCategory = ref('all')

const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'meat', name: '肉类' },
  { id: 'vegetable', name: '蔬菜' },
  { id: 'seafood', name: '海鲜' },
  { id: 'special', name: '特色' }
])

const products = ref([
  {
    id: 1,
    name: '经典牛肉串',
    description: '精选优质牛肉，切片均匀，肉质鲜嫩',
    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    category: 'meat',
    price: 8,
    isRecommended: true
  },
  {
    id: 2,
    name: '特色毛肚',
    description: '新鲜毛肚，口感爽脆，搭配特制蘸料',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    category: 'special',
    price: 12,
    isRecommended: true
  },
  {
    id: 3,
    name: '招牌鸭肠',
    description: '精选鸭肠，处理干净，口感嫩滑',
    image: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    category: 'special',
    price: 10,
    isRecommended: false
  },
  {
    id: 4,
    name: '新鲜土豆片',
    description: '新鲜土豆切片，吸收汤汁，香糯可口',
    image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    category: 'vegetable',
    price: 4,
    isRecommended: false
  },
  {
    id: 5,
    name: '鲜虾滑',
    description: '新鲜虾肉制作，Q弹爽滑，鲜味十足',
    image: 'https://images.unsplash.com/photo-1565958011703-44f9829ba187?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    category: 'seafood',
    price: 15,
    isRecommended: true
  },
  {
    id: 6,
    name: '嫩豆腐',
    description: '嫩滑豆腐，吸收汤汁，口感丰富',
    image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
    category: 'vegetable',
    price: 3,
    isRecommended: false
  }
])

const processSteps = ref([
  {
    id: 1,
    title: '精选食材',
    description: '严格筛选优质食材，确保新鲜度和品质',
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 2,
    title: '专业串制',
    description: '经验丰富的师傅手工串制，保证每串品质',
    image: 'https://images.unsplash.com/photo-1556909114-4f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 3,
    title: '秘制汤底',
    description: '传承川渝传统工艺，熬制香浓汤底',
    image: 'https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 4,
    title: '精心烹煮',
    description: '控制火候和时间，确保最佳口感',
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  }
])

const environments = ref([
  {
    id: 1,
    title: '用餐区域',
    image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'
  },
  {
    id: 2,
    title: '开放式厨房',
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'
  },
  {
    id: 3,
    title: '包间环境',
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'
  },
  {
    id: 4,
    title: '门店外观',
    image: 'https://images.unsplash.com/photo-1559329007-40df8a9345d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80'
  }
])

const filteredProducts = computed(() => {
  if (activeCategory.value === 'all') {
    return products.value
  }
  return products.value.filter(product => product.category === activeCategory.value)
})

const setActiveCategory = (categoryId: string) => {
  activeCategory.value = categoryId
}
</script>

<style scoped lang="scss">
.products-page {
  .page-header {
    @apply bg-gradient-to-r from-primary-500 to-orange-500 text-white;
    padding: 120px 0 80px 0; // 增加顶部padding避免被导航栏遮挡

    .header-content {
      @apply text-center;

      .page-title {
        @apply text-4xl md:text-5xl font-bold mb-4;
      }

      .page-subtitle {
        @apply text-lg md:text-xl opacity-90;
      }
    }
  }
  
  .product-categories {
    @apply py-20;
    
    .category-tabs {
      @apply flex flex-wrap justify-center gap-4 mb-12;
      
      .category-tab {
        @apply px-6 py-3 rounded-full border-2 border-gray-300 text-gray-600 transition-all duration-300;
        @apply hover:border-primary-500 hover:text-primary-500;
        
        &.active {
          @apply bg-primary-500 border-primary-500 text-white;
        }
      }
    }
    
    .products-grid {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
      
      .product-item {
        .product-card {
          @apply bg-white rounded-xl overflow-hidden shadow-card transition-all duration-300;
          @apply hover:shadow-hover hover:transform hover:-translate-y-1;
          
          .product-image {
            @apply relative h-48;
            
            .product-badge {
              @apply absolute top-4 right-4 bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium;
            }
          }
          
          .product-content {
            @apply p-6;
            
            .product-name {
              @apply text-lg font-semibold text-gray-800 mb-2;
            }
            
            .product-description {
              @apply text-gray-600 text-sm mb-4;
            }
            
            .product-price {
              @apply text-primary-500 font-bold text-lg;
            }
          }
        }
      }
    }
  }
  
  .cooking-process {
    @apply py-20 bg-gray-50;
    
    .process-steps {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
      
      .step-item {
        @apply text-center;
        
        .step-image {
          @apply h-48 mb-6;
        }
        
        .step-content {
          .step-number {
            @apply w-12 h-12 bg-primary-500 text-white rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4;
          }
          
          .step-title {
            @apply text-lg font-semibold text-gray-800 mb-2;
          }
          
          .step-description {
            @apply text-gray-600 text-sm;
          }
        }
      }
    }
  }
  
  .store-environment {
    @apply py-20;
    
    .environment-gallery {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
      
      .env-item {
        @apply text-center;
        
        .env-title {
          @apply text-lg font-semibold text-gray-800 mt-4;
        }
      }
    }
  }
}
</style>
