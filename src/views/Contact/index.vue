<template>
  <div class="contact-page">
    <Header />
    
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title" data-aos="fade-up">联系我们</h1>
          <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            我们随时为您提供专业的咨询和服务
          </p>
        </div>
      </div>
    </section>
    
    <!-- 联系信息 -->
    <section class="contact-info">
      <div class="container">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div 
            v-for="(info, index) in contactInfos" 
            :key="info.id"
            class="info-card"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="info-icon">
              <el-icon class="text-3xl">
                <component :is="info.icon" />
              </el-icon>
            </div>
            <h3 class="info-title">{{ info.title }}</h3>
            <div class="info-content">
              <p v-for="item in info.content" :key="item" class="info-item">
                {{ item }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 联系表单 -->
    <section class="contact-form-section">
      <div class="container">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- 表单 -->
          <div class="form-wrapper" data-aos="fade-right">
            <h2 class="form-title">在线留言</h2>
            <p class="form-description">
              请填写以下信息，我们会尽快与您联系
            </p>
            
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              label-position="top"
              class="contact-form"
            >
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="form.name" placeholder="请输入您的姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入您的手机号" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入您的邮箱（选填）" />
              </el-form-item>
              
              <el-form-item label="咨询类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择咨询类型" class="w-full">
                  <el-option label="加盟咨询" value="franchise" />
                  <el-option label="产品咨询" value="product" />
                  <el-option label="投诉建议" value="complaint" />
                  <el-option label="其他咨询" value="other" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="主题" prop="subject">
                <el-input v-model="form.subject" placeholder="请输入咨询主题" />
              </el-form-item>
              
              <el-form-item label="详细内容" prop="message">
                <el-input
                  v-model="form.message"
                  type="textarea"
                  :rows="5"
                  placeholder="请详细描述您的问题或需求"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  size="large" 
                  :loading="submitting"
                  @click="submitForm"
                  class="w-full"
                >
                  提交留言
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 地图 -->
          <div class="map-wrapper" data-aos="fade-left">
            <h2 class="map-title">公司位置</h2>
            <div class="map-container">
              <div class="map-placeholder">
                <el-icon class="text-6xl text-gray-400">
                  <Location />
                </el-icon>
                <p class="text-gray-500 mt-4">地图加载中...</p>
              </div>
            </div>
            <div class="address-info">
              <h3 class="address-title">总部地址</h3>
              <p class="address-text">四川省成都市锦江区春熙路123号</p>
              <p class="address-text">邮编：610000</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { 
  Phone, 
  Message, 
  Location, 
  Clock 
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import { submitContactForm } from '@/api/modules/common'
import type { ContactForm } from '@/types'

const formRef = ref<FormInstance>()
const submitting = ref(false)

const contactInfos = ref([
  {
    id: 1,
    title: '联系电话',
    icon: Phone,
    content: [
      '加盟热线：************',
      '客服电话：028-8888-8888',
      '投诉电话：028-9999-9999'
    ]
  },
  {
    id: 2,
    title: '邮箱地址',
    icon: Message,
    content: [
      '加盟咨询：<EMAIL>',
      '客户服务：<EMAIL>',
      '商务合作：<EMAIL>'
    ]
  },
  {
    id: 3,
    title: '工作时间',
    icon: Clock,
    content: [
      '周一至周五：9:00-18:00',
      '周六至周日：10:00-17:00',
      '法定节假日：10:00-16:00'
    ]
  }
])

const form = reactive<ContactForm>({
  name: '',
  phone: '',
  email: '',
  subject: '',
  message: '',
  type: 'general'
})

const rules = {
  name: [
    { required: true, message: '请输入您的姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入您的手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择咨询类型', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请输入咨询主题', trigger: 'blur' }
  ],
  message: [
    { required: true, message: '请输入详细内容', trigger: 'blur' },
    { min: 10, message: '内容不能少于10个字符', trigger: 'blur' }
  ]
}

const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    await submitContactForm(form)
    
    ElMessage.success('留言提交成功，我们会尽快与您联系！')
    
    // 重置表单
    formRef.value.resetFields()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.contact-page {
  .page-header {
    @apply bg-gradient-to-r from-primary-500 to-orange-500 text-white;
    padding: 120px 0 80px 0; // 增加顶部padding避免被导航栏遮挡
    
    .header-content {
      @apply text-center;
      
      .page-title {
        @apply text-4xl md:text-5xl font-bold mb-4;
      }
      
      .page-subtitle {
        @apply text-lg md:text-xl opacity-90;
      }
    }
  }
  
  .contact-info {
    @apply py-20;
    
    .info-card {
      @apply text-center bg-white rounded-xl p-8 shadow-card transition-all duration-300;
      @apply hover:shadow-hover hover:transform hover:-translate-y-1;
      
      .info-icon {
        @apply w-16 h-16 bg-primary-100 text-primary-500 rounded-full flex items-center justify-center mx-auto mb-6;
      }
      
      .info-title {
        @apply text-xl font-semibold text-gray-800 mb-4;
      }
      
      .info-content {
        .info-item {
          @apply text-gray-600 mb-2;
        }
      }
    }
  }
  
  .contact-form-section {
    @apply py-20 bg-gray-50;
    
    .form-wrapper {
      .form-title {
        @apply text-2xl font-bold text-gray-800 mb-2;
      }
      
      .form-description {
        @apply text-gray-600 mb-8;
      }
      
      .contact-form {
        @apply bg-white rounded-xl p-8 shadow-card;
      }
    }
    
    .map-wrapper {
      .map-title {
        @apply text-2xl font-bold text-gray-800 mb-6;
      }
      
      .map-container {
        @apply h-80 bg-gray-200 rounded-xl mb-6 overflow-hidden;
        
        .map-placeholder {
          @apply h-full flex flex-col items-center justify-center;
        }
      }
      
      .address-info {
        @apply bg-white rounded-xl p-6 shadow-card;
        
        .address-title {
          @apply text-lg font-semibold text-gray-800 mb-3;
        }
        
        .address-text {
          @apply text-gray-600 mb-1;
        }
      }
    }
  }
}
</style>
