<template>
  <div class="franchise-page">
    <Header />
    
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="header-bg">
        <LazyImage
          src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80"
          alt="加盟中心"
          container-class="absolute inset-0"
          image-class="w-full h-full object-cover"
        />
        <div class="header-overlay"></div>
      </div>
      <div class="container relative z-10">
        <div class="header-content">
          <h1 class="page-title" data-aos="fade-up">加盟中心</h1>
          <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            携手你好签签，共创美好未来
          </p>
          <div class="header-actions" data-aos="fade-up" data-aos-delay="400">
            <a href="#apply" class="btn btn-primary btn-large">
              立即申请加盟
            </a>
            <a href="tel:************" class="btn btn-secondary btn-large ml-4">
              咨询热线：************
            </a>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 加盟优势 -->
    <section class="franchise-advantages">
      <div class="container">
        <SectionTitle
          subtitle="加盟优势"
          title="选择我们的理由"
          description="全方位支持，助您轻松创业成功"
        />
        
        <div class="advantages-grid">
          <div 
            v-for="(advantage, index) in advantages" 
            :key="advantage.id"
            class="advantage-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="advantage-card">
              <div class="advantage-icon">
                <el-icon class="text-4xl">
                  <component :is="advantage.icon" />
                </el-icon>
              </div>
              <h3 class="advantage-title">{{ advantage.title }}</h3>
              <p class="advantage-description">{{ advantage.description }}</p>
              <ul class="advantage-features">
                <li v-for="feature in advantage.features" :key="feature">
                  <el-icon class="feature-check">
                    <Check />
                  </el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资分析 -->
    <section id="calculator" class="investment-analysis">
      <div class="container">
        <SectionTitle
          subtitle="投资分析"
          title="投资回报计算"
          description="透明的投资成本，清晰的盈利模式"
        />
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- 投资计算器 -->
          <div class="calculator-wrapper" data-aos="fade-right">
            <div class="calculator-card">
              <h3 class="calculator-title">投资回报计算器</h3>
              <div class="calculator-form">
                <div class="form-group">
                  <label>选择城市类型</label>
                  <el-select v-model="calculator.cityType" class="w-full">
                    <el-option label="一线城市" value="tier1" />
                    <el-option label="二线城市" value="tier2" />
                    <el-option label="三线城市" value="tier3" />
                  </el-select>
                </div>
                <div class="form-group">
                  <label>门店面积</label>
                  <el-select v-model="calculator.storeSize" class="w-full">
                    <el-option label="小型店（50-80㎡）" value="small" />
                    <el-option label="中型店（80-120㎡）" value="medium" />
                    <el-option label="大型店（120-200㎡）" value="large" />
                  </el-select>
                </div>
                <div class="form-group">
                  <label>总投资金额</label>
                  <el-input v-model="calculator.totalInvestment" readonly>
                    <template #suffix>万元</template>
                  </el-input>
                </div>
                <button @click="calculateROI" class="btn btn-primary w-full">
                  计算投资回报
                </button>
              </div>
            </div>
          </div>
          
          <!-- 投资明细 -->
          <div class="investment-details" data-aos="fade-left">
            <div class="details-card">
              <h3 class="details-title">投资明细</h3>
              <div class="details-list">
                <div v-for="item in investmentDetails" :key="item.name" class="detail-item">
                  <span class="detail-name">{{ item.name }}</span>
                  <span class="detail-amount">{{ item.amount }}万元</span>
                </div>
              </div>
              
              <div v-if="roiResult" class="roi-result">
                <h4 class="roi-title">预期收益</h4>
                <div class="roi-stats">
                  <div class="roi-item">
                    <span class="roi-label">月营业额</span>
                    <span class="roi-value">{{ roiResult.monthlyRevenue }}万元</span>
                  </div>
                  <div class="roi-item">
                    <span class="roi-label">月净利润</span>
                    <span class="roi-value">{{ roiResult.monthlyProfit }}万元</span>
                  </div>
                  <div class="roi-item">
                    <span class="roi-label">回本周期</span>
                    <span class="roi-value">{{ roiResult.paybackPeriod }}个月</span>
                  </div>
                  <div class="roi-item">
                    <span class="roi-label">年投资回报率</span>
                    <span class="roi-value">{{ roiResult.roi }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 加盟流程 -->
    <section class="franchise-process">
      <div class="container">
        <SectionTitle
          subtitle="加盟流程"
          title="简单四步，轻松加盟"
          description="标准化的加盟流程，让您的创业之路更加顺畅"
        />
        
        <div class="process-timeline">
          <div 
            v-for="(step, index) in processSteps" 
            :key="step.id"
            class="process-step"
            data-aos="fade-up"
            :data-aos-delay="index * 200"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
            <div v-if="index < processSteps.length - 1" class="step-arrow">
              <el-icon>
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 加盟申请表单 -->
    <section id="apply" class="franchise-application">
      <div class="container">
        <SectionTitle
          subtitle="在线申请"
          title="立即申请加盟"
          description="填写申请信息，我们将在24小时内与您联系"
        />
        
        <div class="application-wrapper">
          <div class="application-card" data-aos="fade-up">
            <el-form
              ref="applicationFormRef"
              :model="applicationForm"
              :rules="applicationRules"
              label-position="top"
              class="application-form"
            >
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="applicationForm.name" placeholder="请输入您的姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号" prop="phone">
                    <el-input v-model="applicationForm.phone" placeholder="请输入您的手机号" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input v-model="applicationForm.email" placeholder="请输入您的邮箱" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="意向城市" prop="city">
                    <el-input v-model="applicationForm.city" placeholder="请输入意向开店城市" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="投资预算" prop="investmentBudget">
                    <el-select v-model="applicationForm.investmentBudget" placeholder="请选择投资预算" class="w-full">
                      <el-option label="50-100万" :value="75" />
                      <el-option label="100-150万" :value="125" />
                      <el-option label="150-200万" :value="175" />
                      <el-option label="200万以上" :value="250" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="意向区域" prop="district">
                    <el-input v-model="applicationForm.district" placeholder="请输入具体区域" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="相关经验" prop="experience">
                <el-input
                  v-model="applicationForm.experience"
                  type="textarea"
                  :rows="3"
                  placeholder="请简述您的餐饮或创业经验"
                />
              </el-form-item>
              
              <el-form-item label="留言" prop="message">
                <el-input
                  v-model="applicationForm.message"
                  type="textarea"
                  :rows="3"
                  placeholder="请留下您的问题或需求"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  size="large" 
                  :loading="submitting"
                  @click="submitApplication"
                  class="w-full"
                >
                  提交申请
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </section>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { 
  Shop, 
  Setting, 
  UserFilled, 
  TrendCharts,
  Check,
  ArrowRight
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'
import { submitFranchiseApplication } from '@/api/modules/franchise'
import type { FranchiseFormData } from '@/types'

const applicationFormRef = ref<FormInstance>()
const submitting = ref(false)

const advantages = ref([
  {
    id: 1,
    title: '品牌支持',
    description: '成熟的品牌体系，完善的VI识别系统',
    icon: Shop,
    features: [
      '统一品牌形象',
      '专业VI设计',
      '品牌宣传支持',
      '市场推广策略'
    ]
  },
  {
    id: 2,
    title: '技术支持',
    description: '专业的技术培训，标准化操作流程',
    icon: Setting,
    features: [
      '核心技术培训',
      '标准化流程',
      '持续技术更新',
      '远程技术支持'
    ]
  },
  {
    id: 3,
    title: '运营支持',
    description: '全方位运营指导，从开店到盈利',
    icon: UserFilled,
    features: [
      '选址指导',
      '装修设计',
      '人员培训',
      '运营管理'
    ]
  },
  {
    id: 4,
    title: '营销支持',
    description: '专业营销策划，提升门店业绩',
    icon: TrendCharts,
    features: [
      '开业策划',
      '促销活动',
      '线上推广',
      '会员管理'
    ]
  }
])

const processSteps = ref([
  {
    id: 1,
    title: '咨询申请',
    description: '在线咨询或电话咨询，提交加盟申请'
  },
  {
    id: 2,
    title: '实地考察',
    description: '到总部或门店实地考察，深入了解项目'
  },
  {
    id: 3,
    title: '签约合作',
    description: '签署加盟合同，缴纳相关费用'
  },
  {
    id: 4,
    title: '开业运营',
    description: '门店装修、人员培训、开业运营'
  }
])

// 投资计算器
const calculator = reactive({
  cityType: 'tier2',
  storeSize: 'medium',
  totalInvestment: 120
})

const roiResult = ref<any>(null)

const investmentDetails = computed(() => {
  const baseDetails = [
    { name: '加盟费', amount: 15 },
    { name: '保证金', amount: 5 },
    { name: '设备费用', amount: 25 },
    { name: '装修费用', amount: 30 },
    { name: '首批物料', amount: 10 },
    { name: '流动资金', amount: 35 }
  ]
  
  // 根据城市类型和门店大小调整费用
  const multiplier = {
    tier1: { small: 1.2, medium: 1.3, large: 1.5 },
    tier2: { small: 1.0, medium: 1.1, large: 1.3 },
    tier3: { small: 0.8, medium: 0.9, large: 1.1 }
  }
  
  const factor = multiplier[calculator.cityType as keyof typeof multiplier][calculator.storeSize as keyof typeof multiplier.tier1]
  
  return baseDetails.map(item => ({
    ...item,
    amount: Math.round(item.amount * factor)
  }))
})

// 监听计算器变化，自动更新总投资
watch([() => calculator.cityType, () => calculator.storeSize], () => {
  calculator.totalInvestment = investmentDetails.value.reduce((sum, item) => sum + item.amount, 0)
}, { immediate: true })

const calculateROI = () => {
  // 模拟计算投资回报
  const baseRevenue = {
    tier1: { small: 35, medium: 50, large: 70 },
    tier2: { small: 25, medium: 35, large: 50 },
    tier3: { small: 18, medium: 25, large: 35 }
  }
  
  const monthlyRevenue = baseRevenue[calculator.cityType as keyof typeof baseRevenue][calculator.storeSize as keyof typeof baseRevenue.tier1]
  const monthlyProfit = Math.round(monthlyRevenue * 0.3)
  const paybackPeriod = Math.round(calculator.totalInvestment / monthlyProfit)
  const roi = Math.round((monthlyProfit * 12 / calculator.totalInvestment) * 100)
  
  roiResult.value = {
    monthlyRevenue,
    monthlyProfit,
    paybackPeriod,
    roi
  }
}

// 加盟申请表单
const applicationForm = reactive<FranchiseFormData>({
  name: '',
  phone: '',
  email: '',
  city: '',
  district: '',
  investmentBudget: undefined,
  experience: '',
  message: ''
})

const applicationRules = {
  name: [
    { required: true, message: '请输入您的姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入您的手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请输入意向城市', trigger: 'blur' }
  ],
  investmentBudget: [
    { required: true, message: '请选择投资预算', trigger: 'change' }
  ]
}

const submitApplication = async () => {
  if (!applicationFormRef.value) return
  
  try {
    await applicationFormRef.value.validate()
    submitting.value = true
    
    await submitFranchiseApplication(applicationForm)
    
    ElMessage.success('申请提交成功，我们会在24小时内与您联系！')
    
    // 重置表单
    applicationFormRef.value.resetFields()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.franchise-page {
  .page-header {
    @apply relative max-h-[800px] min-h-[600px] flex items-center justify-center;
    height: calc(100vh - 0px); // 全屏高度，导航栏透明所以不需要减去导航栏高度
    padding-top: 100px; // 增加顶部padding确保内容不被遮挡
    
    .header-bg {
      @apply absolute inset-0;
    }
    
    .header-overlay {
      @apply absolute inset-0 bg-black bg-opacity-50;
    }
    
    .header-content {
      @apply text-center text-white;
      
      .page-title {
        @apply text-4xl md:text-5xl lg:text-6xl font-bold mb-6;
      }
      
      .page-subtitle {
        @apply text-lg md:text-xl mb-8 opacity-90;
      }
      
      .header-actions {
        @apply flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4;
      }
    }
  }
  
  .franchise-advantages {
    @apply py-20;
    
    .advantages-grid {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
      
      .advantage-item {
        .advantage-card {
          @apply bg-white rounded-xl p-8 shadow-card transition-all duration-300;
          @apply hover:shadow-hover hover:transform hover:-translate-y-2;
          
          .advantage-icon {
            @apply w-16 h-16 bg-primary-100 text-primary-500 rounded-xl flex items-center justify-center mb-6;
          }
          
          .advantage-title {
            @apply text-xl font-semibold text-gray-800 mb-3;
          }
          
          .advantage-description {
            @apply text-gray-600 mb-6;
          }
          
          .advantage-features {
            @apply space-y-2;
            
            li {
              @apply flex items-center space-x-2 text-sm text-gray-600;
              
              .feature-check {
                @apply text-primary-500 text-xs;
              }
            }
          }
          
          &:hover {
            .advantage-icon {
              @apply bg-primary-500 text-white;
            }
          }
        }
      }
    }
  }
  
  .investment-analysis {
    @apply py-20 bg-gray-50;
    
    .calculator-wrapper {
      .calculator-card {
        @apply bg-white rounded-xl p-8 shadow-card;
        
        .calculator-title {
          @apply text-xl font-semibold text-gray-800 mb-6;
        }
        
        .calculator-form {
          .form-group {
            @apply mb-4;
            
            label {
              @apply block text-sm font-medium text-gray-700 mb-2;
            }
          }
        }
      }
    }
    
    .investment-details {
      .details-card {
        @apply bg-white rounded-xl p-8 shadow-card;
        
        .details-title {
          @apply text-xl font-semibold text-gray-800 mb-6;
        }
        
        .details-list {
          @apply space-y-3 mb-6;
          
          .detail-item {
            @apply flex justify-between items-center py-2 border-b border-gray-100;
            
            .detail-name {
              @apply text-gray-600;
            }
            
            .detail-amount {
              @apply font-semibold text-gray-800;
            }
          }
        }
        
        .roi-result {
          @apply pt-6 border-t border-gray-200;
          
          .roi-title {
            @apply text-lg font-semibold text-gray-800 mb-4;
          }
          
          .roi-stats {
            @apply grid grid-cols-2 gap-4;
            
            .roi-item {
              @apply text-center p-3 bg-gray-50 rounded-lg;
              
              .roi-label {
                @apply block text-sm text-gray-600;
              }
              
              .roi-value {
                @apply block text-lg font-bold text-primary-500 mt-1;
              }
            }
          }
        }
      }
    }
  }
  
  .franchise-process {
    @apply py-20;
    
    .process-timeline {
      @apply flex flex-col lg:flex-row items-center justify-center space-y-8 lg:space-y-0 lg:space-x-8;
      
      .process-step {
        @apply flex flex-col lg:flex-row items-center text-center lg:text-left;
        
        .step-number {
          @apply w-16 h-16 bg-primary-500 text-white rounded-full flex items-center justify-center text-xl font-bold mb-4 lg:mb-0 lg:mr-4;
        }
        
        .step-content {
          @apply flex-1;
          
          .step-title {
            @apply text-lg font-semibold text-gray-800 mb-2;
          }
          
          .step-description {
            @apply text-gray-600;
          }
        }
        
        .step-arrow {
          @apply text-primary-500 text-2xl mt-4 lg:mt-0 lg:ml-4;
        }
      }
    }
  }
  
  .franchise-application {
    @apply py-20 bg-gray-50;
    
    .application-wrapper {
      @apply max-w-4xl mx-auto;
      
      .application-card {
        @apply bg-white rounded-xl p-8 shadow-card;
      }
    }
  }
}

// 响应式调整
@media (max-width: 1024px) {
  .franchise-page {
    .process-timeline {
      .process-step {
        .step-arrow {
          @apply transform rotate-90;
        }
      }
    }
  }
}
</style>
