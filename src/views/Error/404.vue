<template>
  <div class="error-page">
    <Header />
    
    <div class="error-content">
      <div class="container">
        <div class="error-wrapper">
          <div class="error-image" data-aos="fade-up">
            <img src="/images/404.svg" alt="页面未找到" class="w-full max-w-md mx-auto" />
          </div>
          
          <div class="error-text" data-aos="fade-up" data-aos-delay="200">
            <h1 class="error-title">404</h1>
            <h2 class="error-subtitle">页面未找到</h2>
            <p class="error-description">
              抱歉，您访问的页面不存在或已被移除。
              <br />
              请检查网址是否正确，或返回首页继续浏览。
            </p>
          </div>
          
          <div class="error-actions" data-aos="fade-up" data-aos-delay="400">
            <router-link to="/" class="btn btn-primary btn-large">
              返回首页
            </router-link>
            <button @click="goBack" class="btn btn-outline btn-large ml-4">
              返回上页
            </button>
          </div>
          
          <div class="quick-links" data-aos="fade-up" data-aos-delay="600">
            <h3 class="quick-links-title">您可能想要访问：</h3>
            <div class="links-grid">
              <router-link to="/about" class="quick-link">
                <el-icon><InfoFilled /></el-icon>
                <span>品牌介绍</span>
              </router-link>
              <router-link to="/products" class="quick-link">
                <el-icon><Food /></el-icon>
                <span>产品展示</span>
              </router-link>
              <router-link to="/franchise" class="quick-link">
                <el-icon><Shop /></el-icon>
                <span>加盟中心</span>
              </router-link>
              <router-link to="/contact" class="quick-link">
                <el-icon><Phone /></el-icon>
                <span>联系我们</span>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  InfoFilled, 
  Food, 
  Shop, 
  Phone 
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped lang="scss">
.error-page {
  @apply min-h-screen flex flex-col;
  
  .error-content {
    @apply flex-1 flex items-center justify-center py-20;
    
    .error-wrapper {
      @apply text-center max-w-2xl mx-auto;
      
      .error-image {
        @apply mb-12;
      }
      
      .error-text {
        @apply mb-12;
        
        .error-title {
          @apply text-8xl md:text-9xl font-bold text-primary-500 mb-4;
        }
        
        .error-subtitle {
          @apply text-2xl md:text-3xl font-bold text-gray-800 mb-6;
        }
        
        .error-description {
          @apply text-gray-600 leading-relaxed;
        }
      }
      
      .error-actions {
        @apply flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 mb-16;
      }
      
      .quick-links {
        .quick-links-title {
          @apply text-lg font-semibold text-gray-800 mb-6;
        }
        
        .links-grid {
          @apply grid grid-cols-2 md:grid-cols-4 gap-4;
          
          .quick-link {
            @apply flex flex-col items-center p-4 bg-gray-50 rounded-lg transition-all duration-300;
            @apply hover:bg-primary-50 hover:text-primary-500;
            
            .el-icon {
              @apply text-2xl mb-2;
            }
            
            span {
              @apply text-sm font-medium;
            }
          }
        }
      }
    }
  }
}
</style>
