<template>
  <div class="about-page">
    <Header />
    
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="header-bg">
        <LazyImage
          src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80"
          alt="品牌介绍"
          container-class="absolute inset-0"
          image-class="w-full h-full object-cover"
        />
        <div class="header-overlay"></div>
      </div>
      <div class="container relative z-10">
        <div class="header-content">
          <h1 class="page-title" data-aos="fade-up">品牌介绍</h1>
          <p class="page-subtitle" data-aos="fade-up" data-aos-delay="200">
            传承正宗川渝串串香文化，融合现代餐饮管理理念
          </p>
        </div>
      </div>
    </section>
    
    <!-- 品牌故事 -->
    <section class="brand-story">
      <div class="container">
        <SectionTitle
          subtitle="品牌故事"
          title="传承与创新的完美结合"
          description="从川渝大地走向全国，用匠心传承美食文化"
        />
        
        <div class="story-content">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="story-text" data-aos="fade-right">
              <p class="story-paragraph">
                你好签签 市井串串始创于川渝大地，承载着深厚的巴蜀文化底蕴。创始人怀着对传统川渝串串香的热爱和敬畏，
                走遍川渝各地，寻访民间高手，学习传统工艺，只为将最正宗的串串香味道带给更多人。
              </p>
              <p class="story-paragraph">
                我们坚持选用优质食材，传承传统制作工艺，同时融入现代餐饮管理理念，为每一位顾客提供正宗、美味、
                健康的串串香体验。从第一家门店到如今遍布全国的连锁品牌，我们始终坚持"品质第一、服务至上"的经营理念。
              </p>
              <p class="story-paragraph">
                今天的你好签签，不仅是一个餐饮品牌，更是川渝文化的传播者。我们致力于将正宗的川渝串串香文化传播到全国各地，
                让更多人感受到川渝美食的魅力。
              </p>
            </div>
            <div class="story-image" data-aos="fade-left">
              <LazyImage
                src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="品牌故事"
                container-class="rounded-xl overflow-hidden shadow-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 发展历程 -->
    <section class="development-history">
      <div class="container">
        <SectionTitle
          subtitle="发展历程"
          title="稳步发展的品牌之路"
          description="见证品牌从创立到壮大的每一个重要时刻"
        />
        
        <div class="timeline">
          <div 
            v-for="(milestone, index) in milestones" 
            :key="milestone.id"
            class="timeline-item"
            :class="{ 'timeline-item-reverse': index % 2 === 1 }"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="timeline-content">
              <div class="timeline-year">{{ milestone.year }}</div>
              <h3 class="timeline-title">{{ milestone.title }}</h3>
              <p class="timeline-description">{{ milestone.description }}</p>
            </div>
            <div class="timeline-image">
              <LazyImage
                :src="milestone.image"
                :alt="milestone.title"
                container-class="rounded-lg overflow-hidden"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 企业文化 -->
    <section class="company-culture">
      <div class="container">
        <SectionTitle
          subtitle="企业文化"
          title="我们的价值观"
          description="用心做好每一串，用情服务每一人"
        />
        
        <div class="culture-grid">
          <div 
            v-for="(culture, index) in cultures" 
            :key="culture.id"
            class="culture-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="culture-icon">
              <el-icon class="text-4xl">
                <component :is="culture.icon" />
              </el-icon>
            </div>
            <h3 class="culture-title">{{ culture.title }}</h3>
            <p class="culture-description">{{ culture.description }}</p>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 荣誉资质 -->
    <section class="honors">
      <div class="container">
        <SectionTitle
          subtitle="荣誉资质"
          title="实力见证品质"
          description="多项荣誉认证，彰显品牌实力"
        />
        
        <div class="honors-grid">
          <div 
            v-for="(honor, index) in honors" 
            :key="honor.id"
            class="honor-item"
            data-aos="fade-up"
            :data-aos-delay="index * 100"
          >
            <div class="honor-image">
              <LazyImage
                :src="honor.image"
                :alt="honor.title"
                container-class="w-full h-full"
              />
            </div>
            <div class="honor-content">
              <h3 class="honor-title">{{ honor.title }}</h3>
              <p class="honor-year">{{ honor.year }}年</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Promotion,
  Star,
  Trophy,
  UserFilled
} from '@element-plus/icons-vue'
import Header from '@/components/layout/Header.vue'
import Footer from '@/components/layout/Footer.vue'
import SectionTitle from '@/components/ui/SectionTitle.vue'
import LazyImage from '@/components/ui/LazyImage.vue'

const milestones = ref([
  {
    id: 1,
    year: '2018',
    title: '品牌创立',
    description: '你好签签品牌正式创立，第一家门店在成都开业',
    image: 'https://images.unsplash.com/photo-1559329007-40df8a9345d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 2,
    year: '2019',
    title: '快速发展',
    description: '门店数量突破50家，覆盖川渝地区主要城市',
    image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 3,
    year: '2021',
    title: '全国扩张',
    description: '开始全国布局，门店数量突破200家',
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 4,
    year: '2023',
    title: '品牌升级',
    description: '完成品牌升级，门店数量突破500家',
    image: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  }
])

const cultures = ref([
  {
    id: 1,
    title: '用心',
    description: '用心选材，用心制作，用心服务每一位顾客',
    icon: Promotion
  },
  {
    id: 2,
    title: '品质',
    description: '坚持品质第一，绝不妥协的品质标准',
    icon: Star
  },
  {
    id: 3,
    title: '创新',
    description: '在传承中创新，在创新中发展',
    icon: Trophy
  },
  {
    id: 4,
    title: '共赢',
    description: '与加盟商、员工、顾客共同成长，实现共赢',
    icon: UserFilled
  }
])

const honors = ref([
  {
    id: 1,
    title: '中国餐饮百强品牌',
    year: 2023,
    image: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 2,
    title: '消费者信赖品牌',
    year: 2023,
    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 3,
    title: '行业创新奖',
    year: 2022,
    image: 'https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  },
  {
    id: 4,
    title: '食品安全示范企业',
    year: 2022,
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80'
  }
])
</script>

<style scoped lang="scss">
.about-page {
  .page-header {
    @apply relative flex items-center justify-center;
    height: 400px; // 固定高度
    padding-top: 100px; // 增加顶部padding避免被导航栏遮挡
    
    .header-bg {
      @apply absolute inset-0;
    }
    
    .header-overlay {
      @apply absolute inset-0 bg-black bg-opacity-50;
    }
    
    .header-content {
      @apply text-center text-white;
      
      .page-title {
        @apply text-4xl md:text-5xl font-bold mb-4;
      }
      
      .page-subtitle {
        @apply text-lg md:text-xl opacity-90;
      }
    }
  }
  
  .brand-story {
    @apply py-20;
    
    .story-content {
      .story-text {
        .story-paragraph {
          @apply text-gray-600 leading-relaxed mb-6;
        }
      }
    }
  }
  
  .development-history {
    @apply py-20 bg-gray-50;
    
    .timeline {
      @apply space-y-16;
      
      .timeline-item {
        @apply grid grid-cols-1 lg:grid-cols-2 gap-12 items-center;
        
        &.timeline-item-reverse {
          .timeline-content {
            @apply lg:order-2;
          }
          
          .timeline-image {
            @apply lg:order-1;
          }
        }
        
        .timeline-content {
          .timeline-year {
            @apply text-primary-500 font-bold text-xl mb-2;
          }
          
          .timeline-title {
            @apply text-2xl font-bold text-gray-800 mb-4;
          }
          
          .timeline-description {
            @apply text-gray-600 leading-relaxed;
          }
        }
        
        .timeline-image {
          @apply h-64;
        }
      }
    }
  }
  
  .company-culture {
    @apply py-20;
    
    .culture-grid {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
      
      .culture-item {
        @apply text-center;
        
        .culture-icon {
          @apply w-20 h-20 bg-primary-100 text-primary-500 rounded-full flex items-center justify-center mx-auto mb-6;
        }
        
        .culture-title {
          @apply text-xl font-semibold text-gray-800 mb-3;
        }
        
        .culture-description {
          @apply text-gray-600 leading-relaxed;
        }
      }
    }
  }
  
  .honors {
    @apply py-20 bg-gray-50;
    
    .honors-grid {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
      
      .honor-item {
        @apply bg-white rounded-xl overflow-hidden shadow-card transition-all duration-300;
        @apply hover:shadow-hover hover:transform hover:-translate-y-1;
        
        .honor-image {
          @apply h-48;
        }
        
        .honor-content {
          @apply p-6 text-center;
          
          .honor-title {
            @apply text-lg font-semibold text-gray-800 mb-2;
          }
          
          .honor-year {
            @apply text-primary-500 font-medium;
          }
        }
      }
    }
  }
}
</style>
