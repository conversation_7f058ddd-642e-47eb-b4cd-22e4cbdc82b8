import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/index.vue'),
    meta: {
      title: '首页 - 你好签签 市井串串',
      description: '你好签签 市井串串官网首页，了解品牌故事，探索加盟机会'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About/index.vue'),
    meta: {
      title: '品牌介绍 - 你好签签 市井串串',
      description: '了解你好签签 市井串串的品牌故事、发展历程和企业文化'
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: () => import('@/views/Products/index.vue'),
    meta: {
      title: '产品展示 - 你好签签 市井串串',
      description: '探索你好签签 市井串串的特色菜品和门店环境'
    }
  },
  {
    path: '/franchise',
    name: 'Franchise',
    component: () => import('@/views/Franchise/index.vue'),
    meta: {
      title: '加盟中心 - 你好签签 市井串串',
      description: '了解你好签签 市井串串加盟政策、投资分析和申请流程'
    }
  },
  {
    path: '/stores',
    name: 'Stores',
    component: () => import('@/views/Stores/index.vue'),
    meta: {
      title: '门店展示 - 你好签签 市井串串',
      description: '查找你好签签 市井串串门店位置和详细信息'
    }
  },
  {
    path: '/news',
    name: 'News',
    component: () => import('@/views/News/index.vue'),
    meta: {
      title: '新闻中心 - 你好签签 市井串串',
      description: '获取你好签签 市井串串最新动态和行业资讯'
    }
  },
  {
    path: '/news/:id',
    name: 'NewsDetail',
    component: () => import('@/views/News/Detail.vue'),
    meta: {
      title: '新闻详情 - 你好签签 市井串串'
    }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('@/views/Contact/index.vue'),
    meta: {
      title: '联系我们 - 你好签签 市井串串',
      description: '联系你好签签 市井串串，获取更多信息和支持'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面未找到 - 你好签签 市井串串'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  
  // 设置页面描述
  if (to.meta?.description) {
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', to.meta.description as string)
    }
  }
  
  next()
})

export default router
