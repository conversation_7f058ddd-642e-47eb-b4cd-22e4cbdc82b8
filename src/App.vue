<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 全局加载组件 -->
    <GlobalLoading v-if="isLoading" />
    
    <!-- 主要内容 -->
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    
    <!-- 回到顶部按钮 -->
    <BackToTop />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import GlobalLoading from '@/components/common/GlobalLoading.vue'
import BackToTop from '@/components/common/BackToTop.vue'

const isLoading = ref(true)

onMounted(() => {
  // 模拟初始化加载
  setTimeout(() => {
    isLoading.value = false
  }, 1000)
})
</script>

<style lang="scss">
// 全局过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 全局字体平滑
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 全局链接样式
a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

// 全局按钮样式重置
button {
  outline: none;
  border: none;
  background: none;
  cursor: pointer;
}

// 全局输入框样式重置
input, textarea {
  outline: none;
  border: none;
  background: none;
}

// 图片默认样式
img {
  max-width: 100%;
  height: auto;
  display: block;
}
</style>
