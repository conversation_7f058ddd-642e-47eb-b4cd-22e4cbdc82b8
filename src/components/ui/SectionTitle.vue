<template>
  <div class="section-title-wrapper" :class="wrapperClass">
    <div class="section-title-content" :class="textAlignClass">
      <!-- 副标题 -->
      <p v-if="subtitle" class="section-subtitle" data-aos="fade-up">
        {{ subtitle }}
      </p>
      
      <!-- 主标题 -->
      <h2 
        class="section-title" 
        :class="titleClass"
        data-aos="fade-up" 
        data-aos-delay="100"
      >
        <slot name="title">{{ title }}</slot>
      </h2>
      
      <!-- 描述 -->
      <p 
        v-if="description" 
        class="section-description" 
        :class="descriptionClass"
        data-aos="fade-up" 
        data-aos-delay="200"
      >
        {{ description }}
      </p>
      
      <!-- 装饰线 -->
      <div 
        v-if="showDecorator" 
        class="section-decorator"
        data-aos="fade-up" 
        data-aos-delay="300"
      >
        <div class="decorator-line"></div>
        <div class="decorator-dot"></div>
        <div class="decorator-line"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
interface Props {
  title?: string
  subtitle?: string
  description?: string
  align?: 'left' | 'center' | 'right'
  showDecorator?: boolean
  wrapperClass?: string
  titleClass?: string
  descriptionClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  description: '',
  align: 'center',
  showDecorator: true,
  wrapperClass: '',
  titleClass: '',
  descriptionClass: ''
})

// 计算文本对齐类名
const textAlignClass = computed(() => {
  switch (props.align) {
    case 'left':
      return 'text-left'
    case 'right':
      return 'text-right'
    case 'center':
    default:
      return 'text-center'
  }
})
</script>

<style scoped lang="scss">
.section-title-wrapper {
  @apply mb-12;
  
  .section-title-content {
    
    .section-subtitle {
      @apply text-primary-500 font-medium text-sm uppercase tracking-wider mb-2;
    }
    
    .section-title {
      @apply text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4;
      @apply leading-tight;
      
      &.text-gradient {
        @apply bg-gradient-to-r from-primary-500 to-orange-500 bg-clip-text text-transparent;
      }
    }
    
    .section-description {
      @apply text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed mb-6;
    }
    
    .section-decorator {
      @apply flex items-center justify-center space-x-4;
      
      .decorator-line {
        @apply w-12 h-0.5 bg-gradient-to-r from-primary-500 to-orange-500;
      }
      
      .decorator-dot {
        @apply w-2 h-2 bg-primary-500 rounded-full;
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .section-title-wrapper {
    .section-title-content {
      .section-title {
        @apply text-2xl md:text-3xl;
      }
      
      .section-description {
        @apply text-base;
      }
    }
  }
}
</style>
