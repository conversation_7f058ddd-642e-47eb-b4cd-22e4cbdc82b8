<template>
  <div class="animated-counter">
    <span class="counter-number" :class="numberClass">
      {{ displayValue }}
    </span>
    <span v-if="suffix" class="counter-suffix" :class="suffixClass">
      {{ suffix }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

interface Props {
  value: number
  duration?: number
  suffix?: string
  prefix?: string
  separator?: string
  decimals?: number
  autoplay?: boolean
  numberClass?: string
  suffixClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  value: 0,
  duration: 2000,
  suffix: '',
  prefix: '',
  separator: ',',
  decimals: 0,
  autoplay: true,
  numberClass: '',
  suffixClass: ''
})

const displayValue = ref(0)
const isAnimating = ref(false)

const formatNumber = (num: number): string => {
  const fixed = num.toFixed(props.decimals)
  const parts = fixed.split('.')
  
  // 添加千位分隔符
  if (props.separator) {
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator)
  }
  
  const result = parts.join('.')
  return props.prefix + result
}

const animateCounter = () => {
  if (isAnimating.value) return
  
  isAnimating.value = true
  const startTime = Date.now()
  const startValue = displayValue.value
  const endValue = props.value
  const duration = props.duration
  
  const animate = () => {
    const now = Date.now()
    const progress = Math.min((now - startTime) / duration, 1)
    
    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    const currentValue = startValue + (endValue - startValue) * easeOutQuart
    
    displayValue.value = Math.floor(currentValue)
    
    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      displayValue.value = endValue
      isAnimating.value = false
    }
  }
  
  requestAnimationFrame(animate)
}

// 监听值变化
watch(() => props.value, () => {
  if (props.autoplay) {
    animateCounter()
  }
})

// 手动触发动画
const startAnimation = () => {
  animateCounter()
}

// 重置计数器
const reset = () => {
  displayValue.value = 0
  isAnimating.value = false
}

onMounted(() => {
  if (props.autoplay) {
    // 延迟启动动画，等待元素进入视口
    setTimeout(() => {
      animateCounter()
    }, 100)
  }
})

// 暴露方法给父组件
defineExpose({
  startAnimation,
  reset
})
</script>

<style scoped lang="scss">
.animated-counter {
  @apply inline-flex items-baseline;
  
  .counter-number {
    @apply font-bold tabular-nums;
    font-variant-numeric: tabular-nums;
  }
  
  .counter-suffix {
    @apply ml-1;
  }
}
</style>
