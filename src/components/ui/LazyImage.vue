<template>
  <div class="lazy-image" :class="containerClass">
    <img
      v-lazy="imageSrc"
      :alt="alt"
      :class="imageClass"
      @load="onLoad"
      @error="onError"
    />
    <div v-if="loading" class="loading-placeholder">
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
    </div>
    <div v-if="error" class="error-placeholder">
      <el-icon class="error-icon">
        <Picture />
      </el-icon>
      <p class="error-text">图片加载失败</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

interface Props {
  src: string
  alt?: string
  placeholder?: string
  errorImage?: string
  containerClass?: string
  imageClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  placeholder: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
  errorImage: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80',
  containerClass: '',
  imageClass: 'w-full h-full object-cover'
})

const loading = ref(true)
const error = ref(false)

const imageSrc = computed(() => {
  if (error.value && props.errorImage) {
    return props.errorImage
  }
  return props.src || props.placeholder
})

const onLoad = () => {
  loading.value = false
  error.value = false
}

const onError = () => {
  loading.value = false
  error.value = true
}
</script>

<style scoped lang="scss">
.lazy-image {
  @apply relative overflow-hidden;
  
  .loading-placeholder,
  .error-placeholder {
    @apply absolute inset-0 flex items-center justify-center bg-gray-100;
  }
  
  .loading-placeholder {
    .loading-spinner {
      .spinner {
        @apply w-8 h-8 border-2 border-gray-300 border-t-primary-500 rounded-full animate-spin;
      }
    }
  }
  
  .error-placeholder {
    @apply flex-col text-gray-400;
    
    .error-icon {
      @apply text-2xl mb-2;
    }
    
    .error-text {
      @apply text-sm;
    }
  }
}
</style>
