<template>
  <div class="global-loading">
    <div class="loading-container">
      <div class="loading-logo">
        <img src="/images/logo.svg" alt="你好签签" class="logo-image" />
      </div>
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <div class="loading-text">
        <p class="text-lg font-medium text-gray-700">加载中...</p>
        <p class="text-sm text-gray-500 mt-2">正在为您准备精彩内容</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 无需额外逻辑
</script>

<style scoped lang="scss">
.global-loading {
  @apply fixed inset-0 bg-white z-50 flex items-center justify-center;
  
  .loading-container {
    @apply text-center;
    
    .loading-logo {
      @apply mb-8;
      
      .logo-image {
        @apply w-20 h-20 mx-auto animate-pulse;
      }
    }
    
    .loading-spinner {
      @apply mb-6;
      
      .spinner {
        @apply w-12 h-12 mx-auto border-4 border-gray-200 border-t-primary-500 rounded-full animate-spin;
      }
    }
    
    .loading-text {
      @apply space-y-2;
    }
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
