<template>
  <header class="header" :class="{ 'header-scrolled': isScrolled }">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <div class="header-logo">
          <router-link to="/" class="logo-link">
            <img src="/你好签签2-logo.png" alt="你好签签" class="logo-image" />
          </router-link>
        </div>

        <!-- 桌面端导航 -->
        <nav class="header-nav desktop-only">
          <ul class="nav-list">
            <li class="nav-item" v-for="item in navItems" :key="item.path">
              <router-link 
                :to="item.path" 
                class="nav-link"
                :class="{ 'nav-link-active': $route.path === item.path }"
              >
                {{ item.name }}
              </router-link>
            </li>
          </ul>
        </nav>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <!-- 联系电话 -->
          <div class="contact-phone desktop-only">
            <el-icon class="phone-icon">
              <Phone />
            </el-icon>
            <span class="phone-number">************</span>
          </div>

          <!-- 加盟按钮 -->
          <router-link to="/franchise" class="franchise-btn btn btn-primary">
            立即加盟
          </router-link>

          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-btn mobile-only" @click="toggleMobileMenu">
            <el-icon class="text-xl">
              <Menu v-if="!mobileMenuOpen" />
              <Close v-else />
            </el-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <transition name="slide-down">
      <div v-show="mobileMenuOpen" class="mobile-menu mobile-only">
        <div class="mobile-nav">
          <router-link 
            v-for="item in navItems" 
            :key="item.path"
            :to="item.path" 
            class="mobile-nav-link"
            @click="closeMobileMenu"
          >
            {{ item.name }}
          </router-link>
        </div>
        <div class="mobile-contact">
          <div class="contact-info">
            <el-icon class="contact-icon">
              <Phone />
            </el-icon>
            <span>************</span>
          </div>
        </div>
      </div>
    </transition>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Phone, Menu, Close } from '@element-plus/icons-vue'

const isScrolled = ref(false)
const mobileMenuOpen = ref(false)

const navItems = [
  { name: '首页', path: '/' },
  { name: '品牌介绍', path: '/about' },
  { name: '产品展示', path: '/products' },
  { name: '加盟中心', path: '/franchise' },
  { name: '门店展示', path: '/stores' },
  { name: '新闻中心', path: '/news' },
  { name: '联系我们', path: '/contact' }
]

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.header {
  @apply fixed top-0 left-0 right-0 z-50 transition-all duration-300;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &.header-scrolled {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .header-content {
    @apply flex items-center justify-between py-6;
  }
  
  .header-logo {
    .logo-link {
      @apply flex items-center;

      .logo-image {
        @apply w-16 h-16;
      }
    }
  }
  
  .header-nav {
    .nav-list {
      @apply flex items-center space-x-10;

      .nav-item {
        .nav-link {
          @apply text-gray-600 font-medium text-lg transition-all duration-300;
          position: relative;
          padding: 8px 0;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #ef4444, #dc2626);
            transition: width 0.3s ease;
          }

          &:hover {
            @apply text-red-600;

            &::after {
              width: 100%;
            }
          }

          &.nav-link-active {
            @apply text-red-600;

            &::after {
              width: 100%;
            }
          }
        }
      }
    }
  }
  
  .header-actions {
    @apply flex items-center space-x-8;

    .contact-phone {
      @apply flex items-center space-x-2 text-gray-600 hover:text-red-600 transition-colors duration-300;

      .phone-icon {
        @apply text-red-500;
      }

      .phone-number {
        @apply font-medium text-lg;
      }
    }

    .franchise-btn {
      @apply bg-gradient-to-r from-red-500 to-red-600 text-white px-8 py-3 rounded-full font-medium transition-all duration-300;
      @apply hover:from-red-600 hover:to-red-700 hover:shadow-xl transform hover:scale-105;
      box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);

      &:hover {
        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
      }
    }

    .mobile-menu-btn {
      @apply p-2 text-gray-600 hover:text-red-600 transition-colors duration-300;
    }
  }
}

.mobile-menu {
  @apply absolute top-full left-0 right-0 shadow-xl border-t;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);

  .mobile-nav {
    @apply py-4;

    .mobile-nav-link {
      @apply block px-6 py-4 text-gray-700 font-medium border-b border-gray-100 transition-all duration-300;
      @apply hover:text-red-600 hover:bg-red-50;

      &:last-child {
        @apply border-b-0;
      }
    }
  }

  .mobile-contact {
    @apply px-6 py-4 border-t;
    background: rgba(248, 250, 252, 0.8);

    .contact-info {
      @apply flex items-center space-x-2 text-gray-600;

      .contact-icon {
        @apply text-red-500;
      }
    }
  }
}

// 动画
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
