import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Banner, SystemConfig, Statistics } from '@/types'
import { getBanners, getSystemConfigs, getStatistics } from '@/api/modules/common'

export const useCommonStore = defineStore('common', () => {
  // 状态
  const banners = ref<Banner[]>([])
  const systemConfigs = ref<SystemConfig[]>([])
  const statistics = ref<Statistics | null>(null)
  const loading = ref(false)

  // 方法
  const fetchBanners = async () => {
    try {
      loading.value = true
      const response = await getBanners()
      banners.value = response.data
    } catch (error) {
      console.error('获取轮播图失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchSystemConfigs = async (group?: string) => {
    try {
      const response = await getSystemConfigs(group)
      systemConfigs.value = response.data
    } catch (error) {
      console.error('获取系统配置失败:', error)
    }
  }

  const fetchStatistics = async () => {
    try {
      const response = await getStatistics()
      statistics.value = response.data
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const getConfigValue = (key: string, defaultValue: any = null) => {
    const config = systemConfigs.value.find(item => item.key === key)
    if (!config) return defaultValue
    
    switch (config.type) {
      case 'number':
        return Number(config.value)
      case 'boolean':
        return config.value === 'true'
      case 'json':
        try {
          return JSON.parse(config.value)
        } catch {
          return defaultValue
        }
      default:
        return config.value
    }
  }

  const initializeCommonData = async () => {
    await Promise.all([
      fetchBanners(),
      fetchSystemConfigs(),
      fetchStatistics()
    ])
  }

  return {
    // 状态
    banners,
    systemConfigs,
    statistics,
    loading,
    
    // 方法
    fetchBanners,
    fetchSystemConfigs,
    fetchStatistics,
    getConfigValue,
    initializeCommonData
  }
})
