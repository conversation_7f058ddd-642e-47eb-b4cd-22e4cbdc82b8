import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const isMobile = ref(false)
  const sidebarCollapsed = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const language = ref<'zh-CN' | 'en-US'>('zh-CN')

  // 计算属性
  const isLoading = computed(() => loading.value)
  const deviceType = computed(() => isMobile.value ? 'mobile' : 'desktop')

  // 方法
  const setLoading = (status: boolean) => {
    loading.value = status
  }

  const setMobile = (status: boolean) => {
    isMobile.value = status
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setSidebarCollapsed = (status: boolean) => {
    sidebarCollapsed.value = status
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新HTML类名
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  const setLanguage = (lang: 'zh-CN' | 'en-US') => {
    language.value = lang
    localStorage.setItem('language', lang)
  }

  const initializeApp = () => {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null
    if (savedTheme) {
      setTheme(savedTheme)
    }

    // 初始化语言
    const savedLanguage = localStorage.getItem('language') as 'zh-CN' | 'en-US' | null
    if (savedLanguage) {
      setLanguage(savedLanguage)
    }

    // 检测设备类型
    const checkMobile = () => {
      setMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
  }

  return {
    // 状态
    loading,
    isMobile,
    sidebarCollapsed,
    theme,
    language,
    
    // 计算属性
    isLoading,
    deviceType,
    
    // 方法
    setLoading,
    setMobile,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    setLanguage,
    initializeApp
  }
})
