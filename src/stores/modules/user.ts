import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface UserInfo {
  id?: string
  username?: string
  email?: string
  avatar?: string
  phone?: string
  role?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo>({})
  const token = ref<string>('')
  const isLoggedIn = ref(false)

  // 计算属性
  const hasUserInfo = computed(() => Object.keys(userInfo.value).length > 0)
  const userName = computed(() => userInfo.value.username || '')
  const userAvatar = computed(() => userInfo.value.avatar || '')

  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
    isLoggedIn.value = !!newToken
  }

  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    localStorage.setItem('userInfo', JSON.stringify(info))
  }

  const login = async (loginData: { username: string; password: string }) => {
    try {
      // 这里应该调用实际的登录API
      // const response = await loginApi(loginData)
      
      // 模拟登录成功
      const mockToken = 'mock-token-' + Date.now()
      const mockUserInfo: UserInfo = {
        id: '1',
        username: loginData.username,
        email: `${loginData.username}@example.com`,
        avatar: '',
        role: 'user'
      }

      setToken(mockToken)
      setUserInfo(mockUserInfo)
      
      return { success: true, data: mockUserInfo }
    } catch (error) {
      console.error('Login failed:', error)
      return { success: false, error: 'Login failed' }
    }
  }

  const logout = () => {
    token.value = ''
    userInfo.value = {}
    isLoggedIn.value = false
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  const initializeUser = () => {
    // 从localStorage恢复用户信息
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')

    if (savedToken) {
      token.value = savedToken
      isLoggedIn.value = true
    }

    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('Failed to parse saved user info:', error)
        localStorage.removeItem('userInfo')
      }
    }
  }

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,
    
    // 计算属性
    hasUserInfo,
    userName,
    userAvatar,
    
    // 方法
    setToken,
    setUserInfo,
    login,
    logout,
    initializeUser
  }
})
