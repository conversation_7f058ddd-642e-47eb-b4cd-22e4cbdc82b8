{"name": "hello-qianqian-website", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "aos": "^2.3.4", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "swiper": "^11.0.5", "vue": "^3.4.0", "vue-router": "^4.2.5", "vue3-lazyload": "^0.3.8"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "sass-embedded": "^1.89.2", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.8", "vite-plugin-windicss": "^1.9.3", "vue-tsc": "^1.8.25", "windicss": "^3.5.6"}}