# 你好签签 市井串串官网

基于 Vue.js 3 + TypeScript + Vite 构建的现代化企业门户网站，专为"你好签签 市井串串"品牌打造。

## 🚀 项目特色

- **现代化技术栈**：Vue.js 3 + TypeScript + Vite + Element Plus + Tailwind CSS
- **响应式设计**：完美适配PC端、平板端、移动端
- **高性能优化**：页面加载≤3秒，图片懒加载，代码分割
- **SEO友好**：完善的meta标签和结构化数据
- **组件化开发**：高度可复用的组件库
- **类型安全**：完整的TypeScript类型定义

## 📋 功能模块

### 🏠 首页
- 轮播图展示
- 品牌核心价值
- 数据统计展示
- 产品特色介绍
- 加盟优势展示
- 成功案例分享
- 最新新闻动态
- 快捷加盟入口

### 🏢 品牌介绍
- 品牌故事
- 发展历程
- 企业文化
- 荣誉资质

### 🍢 产品展示
- 菜品分类展示
- 制作工艺介绍
- 门店环境展示
- 产品特色说明

### 🤝 加盟中心
- 加盟优势介绍
- 投资分析计算器
- 加盟流程说明
- 在线申请表单
- 常见问题解答

### 🏪 门店展示
- 门店地图定位
- 门店信息查询
- 特色门店展示
- 导航功能

### 📰 新闻中心
- 文章列表展示
- 分类筛选功能
- 搜索功能
- 文章详情页面
- 热门文章推荐

### 📞 联系我们
- 多渠道联系方式
- 在线留言表单
- 公司位置地图
- 工作时间说明

## 🛠️ 技术栈

### 前端框架
- **Vue.js 3** - 渐进式JavaScript框架
- **TypeScript** - JavaScript的超集，提供类型安全
- **Vite** - 下一代前端构建工具

### UI组件库
- **Element Plus** - 基于Vue 3的组件库
- **Tailwind CSS** - 实用优先的CSS框架

### 状态管理
- **Pinia** - Vue的状态管理库

### 路由管理
- **Vue Router 4** - Vue.js的官方路由

### 工具库
- **Axios** - HTTP客户端
- **Day.js** - 轻量级日期处理库
- **Lodash-ES** - 实用工具库
- **Swiper** - 轮播图组件
- **AOS** - 滚动动画库

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Husky** - Git钩子工具

## 📦 项目结构

```
src/
├── api/                    # API接口
│   ├── index.ts           # 请求配置
│   └── modules/           # 接口模块
├── assets/                # 静态资源
├── components/            # 公共组件
│   ├── common/           # 通用组件
│   ├── layout/           # 布局组件
│   └── ui/               # UI组件
├── router/               # 路由配置
├── stores/               # 状态管理
├── styles/               # 样式文件
├── types/                # 类型定义
├── utils/                # 工具函数
├── views/                # 页面组件
│   ├── Home/            # 首页
│   ├── About/           # 品牌介绍
│   ├── Products/        # 产品展示
│   ├── Franchise/       # 加盟中心
│   ├── Stores/          # 门店展示
│   ├── News/            # 新闻中心
│   ├── Contact/         # 联系我们
│   └── Error/           # 错误页面
├── App.vue              # 根组件
└── main.ts              # 入口文件
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发环境
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 预览生产版本
```bash
npm run preview
# 或
yarn preview
```

### 代码检查
```bash
npm run lint
# 或
yarn lint
```

### 代码格式化
```bash
npm run format
# 或
yarn format
```

## 🎨 设计规范

### 色彩系统
- **主色调**：签签红 (#E53E3E)
- **辅助色**：暖橙色 (#FF8C00)、金黄色 (#FFD700)
- **中性色**：灰色系列

### 字体规范
- **中文字体**：思源黑体、苹方、微软雅黑
- **英文字体**：Montserrat、Open Sans
- **字号**：12px - 48px

### 布局规范
- **容器最大宽度**：1200px
- **栅格系统**：12列
- **间距系统**：8px的倍数

## 📱 响应式设计

### 断点设置
- **超小屏 (XS)**：<576px
- **小屏 (SM)**：576px - 767px
- **中屏 (MD)**：768px - 991px
- **大屏 (LG)**：992px - 1199px
- **超大屏 (XL)**：≥1200px

## 🔧 配置说明

### 环境变量
```bash
# .env
VITE_APP_TITLE=你好签签 市井串串
VITE_API_BASE_URL=http://localhost:8080/api
VITE_MAP_KEY=your_map_api_key
```

### 代理配置
开发环境下，API请求会自动代理到后端服务器。

## 📈 性能优化

### 已实现的优化
- **代码分割**：按路由和组件分割
- **懒加载**：图片和组件懒加载
- **资源压缩**：Gzip/Brotli压缩
- **缓存策略**：浏览器缓存优化
- **预加载**：关键资源预加载

### 性能指标
- **首屏加载时间**：≤3秒
- **页面切换时间**：≤1秒
- **图片加载优化**：懒加载 + 渐进式加载

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **官网**：https://www.hellochuan.com
- **邮箱**：<EMAIL>
- **电话**：400-123-4567

---

© 2024 你好签签 市井串串. 保留所有权利.
