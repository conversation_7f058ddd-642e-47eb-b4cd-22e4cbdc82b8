<svg width="960" height="400" viewBox="0 0 960 400" xmlns="http://www.w3.org/2000/svg">
  <!-- 你好签签 logo -->
  <defs>
    <style>
      .logo-text {
        fill: #C41E3A;
        font-family: "Sim<PERSON><PERSON>", "Microsoft YaHei", "PingFang SC", sans-serif;
        font-weight: bold;
      }
      .main-text {
        font-size: 120px;
        letter-spacing: 8px;
      }
      .sub-text {
        font-size: 32px;
        fill: #333;
        letter-spacing: 4px;
      }
    </style>
  </defs>

  <!-- 主标题：你好签签 -->
  <text x="480" y="200" text-anchor="middle" class="logo-text main-text">你好签签</text>

  <!-- 副标题：—(市)-(井)-(串)-(串)— -->
  <text x="480" y="260" text-anchor="middle" class="sub-text">—(市)-(井)-(串)-(串)—</text>
</svg>
