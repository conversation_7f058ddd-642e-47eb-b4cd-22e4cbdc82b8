# 你好签签官网 - 宝塔部署文档

## 📋 部署概述

本文档详细介绍如何在宝塔面板上部署"你好签签"官网项目。该项目基于 Vue 3 + TypeScript + Vite 构建，是一个纯前端静态网站。

## 🛠 环境要求

### 服务器要求
- **操作系统**: CentOS 7+ / Ubuntu 18+ / Debian 9+
- **内存**: 最低 1GB，推荐 2GB+
- **存储**: 最低 10GB 可用空间
- **网络**: 公网 IP 和域名（可选）

### 宝塔面板要求
- **宝塔版本**: 7.0+ 
- **必需软件**: Nginx 1.18+, PM2 管理器
- **可选软件**: SSL 证书管理

## 📦 部署前准备

### 1. 安装宝塔面板

```bash
# CentOS 安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian 安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2. 安装必需软件

在宝塔面板 → 软件商店中安装：
- ✅ **Nginx** (版本 1.18+)
- ✅ **PM2 管理器** (用于 Node.js 环境)
- ✅ **Node.js 版本管理器** (安装 Node.js 18+)

### 3. 配置 Node.js 环境

1. 进入 **软件商店** → **运行环境** → **Node.js 版本管理器**
2. 安装 Node.js 18.x 或更高版本
3. 设置为默认版本

## 🚀 部署步骤

### 步骤 1: 创建网站

1. 登录宝塔面板
2. 点击 **网站** → **添加站点**
3. 填写配置：
   - **域名**: 您的域名（如：www.hello-qianqian.com）
   - **根目录**: `/www/wwwroot/hello-qianqian`
   - **PHP版本**: 纯静态（不选择）
   - **数据库**: 不创建

### 步骤 2: 上传项目文件

#### 方法一：通过宝塔文件管理器上传

1. 在本地项目根目录打包项目：
```bash
# 排除 node_modules 和 .git
tar -czf hello-qianqian-source.tar.gz --exclude=node_modules --exclude=.git .
```

2. 通过宝塔面板 → **文件** 上传到 `/www/wwwroot/hello-qianqian/`
3. 解压文件

#### 方法二：通过 Git 克隆（推荐）

1. 在宝塔面板 → **文件** 中进入 `/www/wwwroot/hello-qianqian/`
2. 点击 **终端** 按钮
3. 执行 Git 克隆：
```bash
# 如果有 Git 仓库
git clone [您的仓库地址] .

# 或者直接上传文件后解压
```

### 步骤 3: 安装依赖和构建

1. 在网站根目录打开终端
2. 安装项目依赖：
```bash
npm install
```

3. 构建生产版本：
```bash
npm run build
```

构建完成后，会在项目根目录生成 `dist` 文件夹，这就是要部署的静态文件。

### 步骤 4: 配置 Nginx

1. 在宝塔面板 → **网站** → 找到您的站点 → 点击 **设置**
2. 点击 **网站目录** 标签
3. 修改 **运行目录** 为 `/dist`（指向构建后的静态文件目录）
4. 点击 **伪静态** 标签，添加 Vue Router 配置：

```nginx
location / {
  try_files $uri $uri/ /index.html;
}

# 静态资源缓存配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
  expires 1y;
  add_header Cache-Control "public, immutable";
  add_header Access-Control-Allow-Origin "*";
}

# Gzip 压缩配置
location ~* \.(js|css|html)$ {
  gzip on;
  gzip_vary on;
  gzip_min_length 1024;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

### 步骤 5: SSL 证书配置（推荐）

1. 在网站设置中点击 **SSL** 标签
2. 选择证书类型：
   - **Let's Encrypt**: 免费证书（推荐）
   - **其他证书**: 如果您有付费证书
3. 申请并部署证书
4. 开启 **强制HTTPS**

## ⚙️ 高级配置

### 性能优化配置

在 Nginx 配置中添加以下优化：

```nginx
# 在 server 块中添加
client_max_body_size 10M;

# 开启 Gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;

# 静态文件缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}

# 安全头配置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 自动化部署脚本

创建部署脚本 `deploy.sh`：

```bash
#!/bin/bash
echo "开始部署你好签签官网..."

# 进入项目目录
cd /www/wwwroot/hello-qianqian

# 拉取最新代码（如果使用 Git）
# git pull origin main

# 安装依赖
npm install

# 构建项目
npm run build

# 重启 Nginx（如果需要）
# systemctl reload nginx

echo "部署完成！"
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 构建失败
**问题**: `npm run build` 失败
**解决方案**:
- 检查 Node.js 版本是否为 18+
- 清除缓存：`npm cache clean --force`
- 删除 node_modules 重新安装：`rm -rf node_modules && npm install`

#### 2. 页面刷新 404
**问题**: 直接访问路由地址出现 404
**解决方案**: 确保已正确配置 Nginx 伪静态规则

#### 3. 静态资源加载失败
**问题**: CSS/JS 文件 404
**解决方案**: 检查网站运行目录是否正确指向 `/dist`

#### 4. 内存不足
**问题**: 构建过程中内存溢出
**解决方案**: 
```bash
# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

## 📊 监控和维护

### 1. 性能监控
- 使用宝塔面板的 **监控** 功能查看服务器资源使用情况
- 定期检查网站访问日志

### 2. 备份策略
- 设置宝塔自动备份：**计划任务** → **备份网站**
- 建议每日备份网站文件和数据库（如有）

### 3. 更新流程
1. 本地开发完成后构建测试
2. 上传新的构建文件到服务器
3. 备份当前版本
4. 替换 `dist` 目录内容
5. 测试网站功能

## 🎯 部署检查清单

部署完成后，请检查以下项目：

- [ ] 网站可以正常访问
- [ ] 所有页面路由正常工作
- [ ] 静态资源（图片、CSS、JS）正常加载
- [ ] 移动端响应式布局正常
- [ ] SSL 证书正常工作（如已配置）
- [ ] 网站加载速度 < 3秒
- [ ] 浏览器控制台无错误信息

## 📞 技术支持

如果在部署过程中遇到问题，可以：
1. 查看宝塔面板的错误日志
2. 检查 Nginx 错误日志：`/www/wwwroot/hello-qianqian/logs/`
3. 查看浏览器开发者工具的控制台信息

---

**部署完成！** 🎉 您的"你好签签"官网现在已经成功部署在宝塔服务器上了。
