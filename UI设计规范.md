# 你好签签 市井串串官网UI设计规范

## 1. 设计理念

### 1.1 品牌定位
「你好签签 市井串串」作为新兴的串串香加盟品牌，致力于传承正宗川渝串串香文化，融合现代餐饮管理理念。品牌形象需要体现：
- **传统与现代的融合**：保持川渝文化底蕴，融入现代设计元素
- **温暖与专业并重**：既有市井烟火气，又具备商业专业性
- **亲和力与信赖感**：让用户感受到品牌的温度和可靠性

### 1.2 设计原则
- **简洁明了**：界面简洁，信息层次清晰
- **一致性**：保持视觉和交互的一致性
- **易用性**：操作简单直观，用户体验流畅
- **品牌化**：强化品牌识别，提升品牌记忆度
- **响应式**：适配多种设备和屏幕尺寸

## 2. 色彩系统

### 2.1 主色调

#### 2.1.1 品牌主色
- **签签红 (Primary Red)**
  - 色值：#E53E3E
  - RGB：229, 62, 62
  - 使用场景：主要按钮、重要标题、品牌标识
  - 寓意：热情、活力、食欲

#### 2.1.2 辅助主色
- **深红色 (Dark Red)**
  - 色值：#C53030
  - RGB：197, 48, 48
  - 使用场景：按钮悬停状态、重要信息强调
  - 寓意：稳重、可靠

- **浅红色 (Light Red)**
  - 色值：#FEB2B2
  - RGB：254, 178, 178
  - 使用场景：背景色、提示信息、装饰元素
  - 寓意：温暖、亲和

### 2.2 辅助色彩

#### 2.2.1 暖色系
- **暖橙色 (Warm Orange)**
  - 色值：#FF8C00
  - 使用场景：特色标签、促销信息
  - 寓意：温暖、食欲

- **金黄色 (Golden Yellow)**
  - 色值：#FFD700
  - 使用场景：荣誉展示、价值标签
  - 寓意：品质、价值

#### 2.2.2 中性色系
- **深灰色 (Dark Gray)**
  - 色值：#2D3748
  - 使用场景：主要文本、标题
  - 寓意：专业、稳重

- **中灰色 (Medium Gray)**
  - 色值：#718096
  - 使用场景：次要文本、说明文字
  - 寓意：平衡、中性

- **浅灰色 (Light Gray)**
  - 色值：#F7FAFC
  - 使用场景：背景色、分割线
  - 寓意：简洁、现代

### 2.3 功能色彩

#### 2.3.1 状态色
- **成功绿 (Success Green)**：#38A169
- **警告橙 (Warning Orange)**：#DD6B20
- **错误红 (Error Red)**：#E53E3E
- **信息蓝 (Info Blue)**：#3182CE

#### 2.3.2 色彩使用规范
- 主色调占比不超过30%
- 辅助色占比不超过20%
- 中性色作为主要背景色
- 功能色仅用于特定状态提示

## 3. 字体系统

### 3.1 字体选择

#### 3.1.1 中文字体
- **主字体**：思源黑体 (Source Han Sans)
  - 备选：PingFang SC、苹方、微软雅黑
  - 特点：现代、清晰、易读

- **装饰字体**：站酷庆科黄油体
  - 使用场景：特殊标题、品牌宣传
  - 特点：活泼、有趣、具有亲和力

#### 3.1.2 英文字体
- **主字体**：Montserrat
  - 特点：现代、几何、专业
  - 使用场景：标题、重要文本

- **辅助字体**：Open Sans
  - 特点：友好、易读、通用
  - 使用场景：正文、说明文字

### 3.2 字号规范

#### 3.2.1 桌面端字号
- **H1 主标题**：48px / 3rem
- **H2 副标题**：36px / 2.25rem
- **H3 小标题**：24px / 1.5rem
- **H4 段落标题**：20px / 1.25rem
- **正文**：16px / 1rem
- **小字**：14px / 0.875rem
- **辅助文字**：12px / 0.75rem

#### 3.2.2 移动端字号
- **H1 主标题**：32px / 2rem
- **H2 副标题**：28px / 1.75rem
- **H3 小标题**：20px / 1.25rem
- **H4 段落标题**：18px / 1.125rem
- **正文**：16px / 1rem
- **小字**：14px / 0.875rem
- **辅助文字**：12px / 0.75rem

### 3.3 字重规范
- **Bold (700)**：主标题、重要信息
- **SemiBold (600)**：副标题、强调文字
- **Medium (500)**：导航、按钮文字
- **Regular (400)**：正文、一般文字
- **Light (300)**：辅助文字、说明文字

### 3.4 行高规范
- **标题行高**：1.2-1.3倍字号
- **正文行高**：1.5-1.6倍字号
- **小字行高**：1.4-1.5倍字号

## 4. 布局系统

### 4.1 栅格系统

#### 4.1.1 基础栅格
- **容器最大宽度**：1200px
- **栅格列数**：12列
- **列间距 (Gutter)**：24px
- **外边距 (Margin)**：24px

#### 4.1.2 响应式断点
- **超大屏 (XL)**：≥1200px
- **大屏 (LG)**：992px - 1199px
- **中屏 (MD)**：768px - 991px
- **小屏 (SM)**：576px - 767px
- **超小屏 (XS)**：<576px

#### 4.1.3 断点适配
```css
/* 超大屏 */
@media (min-width: 1200px) {
  .container { max-width: 1200px; }
}

/* 大屏 */
@media (min-width: 992px) and (max-width: 1199px) {
  .container { max-width: 960px; }
}

/* 中屏 */
@media (min-width: 768px) and (max-width: 991px) {
  .container { max-width: 720px; }
}

/* 小屏 */
@media (min-width: 576px) and (max-width: 767px) {
  .container { max-width: 540px; }
}

/* 超小屏 */
@media (max-width: 575px) {
  .container { width: 100%; padding: 0 16px; }
}
```

### 4.2 间距系统

#### 4.2.1 间距规范
- **超大间距 (XXL)**：64px / 4rem
- **大间距 (XL)**：48px / 3rem
- **中等间距 (LG)**：32px / 2rem
- **标准间距 (MD)**：24px / 1.5rem
- **小间距 (SM)**：16px / 1rem
- **微小间距 (XS)**：8px / 0.5rem
- **极小间距 (XXS)**：4px / 0.25rem

#### 4.2.2 间距使用原则
- 相关元素间距较小
- 不相关元素间距较大
- 保持垂直韵律感
- 移动端适当减小间距

### 4.3 圆角规范

#### 4.3.1 圆角尺寸
- **大圆角**：12px - 卡片、模态框
- **中圆角**：8px - 按钮、输入框
- **小圆角**：4px - 标签、小元素
- **微圆角**：2px - 边框、分割线

#### 4.3.2 圆角使用原则
- 保持一致性
- 根据元素大小选择合适圆角
- 避免过度使用圆角

## 5. 组件规范

### 5.1 按钮组件

#### 5.1.1 按钮类型
- **主要按钮 (Primary)**
  - 背景色：#E53E3E
  - 文字色：#FFFFFF
  - 使用场景：主要操作、提交表单

- **次要按钮 (Secondary)**
  - 背景色：透明
  - 边框色：#E53E3E
  - 文字色：#E53E3E
  - 使用场景：次要操作、取消操作

- **文字按钮 (Text)**
  - 背景色：透明
  - 文字色：#E53E3E
  - 使用场景：链接、轻量操作

#### 5.1.2 按钮尺寸
- **大按钮**：高度48px，内边距16px 32px
- **中按钮**：高度40px，内边距12px 24px
- **小按钮**：高度32px，内边距8px 16px

#### 5.1.3 按钮状态
- **正常状态**：默认样式
- **悬停状态**：背景色加深10%
- **激活状态**：背景色加深20%
- **禁用状态**：透明度50%，不可点击

### 5.2 表单组件

#### 5.2.1 输入框
- **高度**：40px
- **内边距**：12px 16px
- **边框**：1px solid #E2E8F0
- **圆角**：8px
- **字号**：16px

#### 5.2.2 表单状态
- **正常状态**：边框色#E2E8F0
- **聚焦状态**：边框色#E53E3E，阴影效果
- **错误状态**：边框色#E53E3E，错误提示
- **禁用状态**：背景色#F7FAFC，不可编辑

#### 5.2.3 标签规范
- **字号**：14px
- **颜色**：#4A5568
- **间距**：标签下方8px间距
- **必填标识**：红色星号(*)

### 5.3 卡片组件

#### 5.3.1 基础卡片
- **背景色**：#FFFFFF
- **边框**：1px solid #E2E8F0
- **圆角**：12px
- **阴影**：0 4px 6px rgba(0, 0, 0, 0.1)
- **内边距**：24px

#### 5.3.2 卡片变体
- **简单卡片**：无阴影，仅边框
- **悬浮卡片**：悬停时阴影加深
- **图片卡片**：包含图片的卡片布局

## 6. 图标系统

### 6.1 图标风格
- **线性图标**：主要使用风格
- **线条粗细**：2px
- **圆角**：2px
- **尺寸规格**：16px、20px、24px、32px

### 6.2 图标使用规范
- 保持风格一致
- 合理使用颜色
- 注意图标语义
- 提供替代文字

## 7. 图片规范

### 7.1 图片尺寸
- **轮播图**：1920×800px
- **产品图**：400×300px (4:3)
- **门店图**：600×400px (3:2)
- **新闻配图**：800×450px (16:9)
- **头像**：100×100px (1:1)

### 7.2 图片质量
- **格式**：WebP优先，JPEG备选
- **压缩**：保持视觉质量前提下最大压缩
- **分辨率**：支持2x高清显示
- **加载**：懒加载，渐进式加载

---

**文档版本**：v1.0  
**最后更新**：2024年1月1日  
**文档状态**：待评审  
**负责人**：设计团队
